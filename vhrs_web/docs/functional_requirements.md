# 📄 **Functional Requirements — Web Dashboard**

---

## **User Roles**

- **Admin** - Full system access
- **User** - Basic access to own data
- **Custom Agent Roles** - Created by admins with specific permissions (e.g., data-agent)

---

## **Functional Requirements by Role**

---

### **1. Admin**

- Manage Vehicles
- Manage Users and Roles
- Assign and Switch Roles on Accounts
- Manage Permissions
- Manage Alerts
- View and Manage Audit Logs
- Request a Vehicle History Record just like the standard users
- View Vehicle History Records
- Manage System Settings
- View System Reports and Statistics

---

### **2. Agents (Custom Roles)**

Ad<PERSON> can create custom agent roles with specific permissions. For example, a "data-agent" role might have these capabilities:

- Manage, Input and Edit Vehicle Data
- View and Respond to Submitted Alerts
- Verify Alerts (Accidents, Fraud, Mileage Tampering)
- Manage Vehicle Records (updates, ownership, maintenance)
- Submit Internal Alerts

---

### **3. Standard User**

- Request Vehicle Reports (by VIN, Plate, etc.)
- View Past Requested Reports
- Submit Alerts (Accident, Mileage Fraud, etc.)
- View Own Submitted Alerts
- Track Status of Requested Reports
- Track Status of Submitted Alerts

---

## **Functional Requirements Descriptions**

### **Admin Functional Requirements**

1. **Manage Users and Roles**
    - Create new accounts for Data Agents and Admins (Standard Users create their own accounts via signup).
    - Assign roles during account creation.
2. **Manage Permissions**
    - Create roles and assign predefined permissions.
3. **Assign Roles to Accounts**
    - Assign Data Agent or Admin roles to user accounts.
4. **Request Vehicle History Records**
    - Admins can search for and request vehicle history records (similar to Standard Users).
5. **View Vehicle History Records**
    - Access the vehicle history records generated by the system based on user or admin requests.
6. **Manage Alerts**
    - View and optionally act on alerts (accidents, fraud, etc.).
7. **View and Manage Audit Logs**
    - Monitor user actions and system events.
8. **Manage System Settings**
    - Configure settings like alert categories, data sources, system rules.
9. **View System Reports and Statistics**
    - Dashboard of system activity, vehicle stats, fraud detection rates and usage statistics.

---

### **Agent Functional Requirements (Example for a data-agent role)**

1. **View and Respond to Submitted Alerts**
    - Review user-submitted alerts (accidents, mileage fraud, etc.).
    - Validate the accuracy of alert information.
2. **Suggest New Vehicle Entries (from Alerts)**
    - If an alert references a vehicle not found in the database (e.g., a reported car by plate number that is not registered), the agent can suggest adding a new vehicle entry with available information from the alert.
3. **Input and Edit Vehicle Data**
    - Edit or complete vehicle records, especially for vehicles suggested through alerts.
4. **Manage Vehicle Data**
    - Maintain, update, or correct vehicle information when necessary.
5. **Submit Internal Alerts**
    - Create internal alerts when identifying data inconsistencies or other issues.

---

### **Standard User Functional Requirements**

1. **Request Vehicle History Reports**
    - Enter or Search by Plate, VIN, or Chassis Number to request a vehicle’s official history report.
    - If a vehicle can’t be found user is informed that no report of vehicle is found.
2. **View Past Requested Vehicle History Reports**
    - Access a personal history of previously requested vehicle history reports.
3. **Submit Alerts**
    - Report accidents, fraud, or any other suspicious vehicle-related activity.
4. **View Own Submitted Alerts**
    - View a list and details of all alerts they have submitted.
5. **Track Status of Requested History Reports**
    - Monitor the progress ("Processing", "Ready", etc.) of their requested vehicle history reports.
6. **Track Status of Submitted Alerts**
    - Follow up on investigations or updates related to their submitted alerts.

---

## 📋 **Functional Requirements Table**

| ID | Role | Functional Requirement | Description |
| --- | --- | --- | --- |
| FR1 | Admin | Manage Users and Roles | Create agent roles and admin accounts, assign roles. |
| FR2 | Admin | Manage Permissions | Create roles and assign predefined permissions. |
| FR3 | Admin | Assign Roles to Accounts | Assign roles during or after account creation. |
| FR4 | Admin | Request Vehicle History Records | Search and request vehicle history records like standard users. |
| FR5 | Admin | View Vehicle History Records | View automatically generated vehicle history records. |
| FR6 | Admin | Manage Alerts | View and optionally act on user alerts. |
| FR7 | Admin | View and Manage Audit Logs | Review system activity logs. |
| FR8 | Admin | Manage System Settings | Configure system operational settings. |
| FR9 | Admin | View System Reports and Statistics | Access dashboards and statistical summaries. |
| FR10 | Agent | View and Respond to Submitted Alerts | Review and act on user-submitted alerts. |
| FR11 | Agent | Suggest New Vehicle Entries (from Alerts) | Propose new vehicle records based on user-reported alerts. |
| FR12 | Agent | Input and Edit Vehicle Data | Edit and complete vehicle records as needed. |
| FR13 | Agent | Manage Vehicle Data | Maintain and correct vehicle data. |
| FR14 | Agent | Submit Internal Alerts | Create alerts for internal data issues. |
| FR15 | Standard User | Request Vehicle History Records | Search and request vehicle history reports. |
| FR16 | Standard User | View Past Requested Vehicle History Records | View history of past vehicle history requests. |
| FR17 | Standard User | Submit Alerts | Submit reports about accidents, fraud, etc. |
| FR18 | Standard User | View Own Submitted Alerts | View their submitted alerts. |
| FR19 | Standard User | Track Status of Requested History Records | Monitor the status of requested vehicle records. |
| FR20 | Standard User | Track Status of Submitted Alerts | Monitor the progress of their submitted alerts. |

---
