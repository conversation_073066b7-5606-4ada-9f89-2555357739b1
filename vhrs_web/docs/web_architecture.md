# **Web Dashboard Architecture Document**

**Version:** 1.0

**Stack:** Next.js + Supabase (Auth + PostgreSQL) + shadcn/ui

---

## **1. Overview**

This web application serves as the administrative dashboard for the Uganda Vehicle History System. It facilitates management of vehicles, users, alerts, and audit logs using Supabase’s backend capabilities. The frontend will be built using **Next.js** for performance and scalability, and **shadcn/ui** for consistent and accessible UI components.

---

## **2. Tech Stack**

| Layer | Technology | Notes |
| --- | --- | --- |
| Frontend | **Next.js (App Router)** | SSR, routing, API routes, and static optimization |
| UI Components | **shadcn/ui + Tailwind CSS** | Modern and accessible UI components with full customization |
| Auth & Backend | **Supabase** | Supabase Auth (JWT + RLS), PostgreSQL with PostGIS, Row-Level Security |
| State Management | **React Hooks + Context** | Minimal external state libraries; per-page context when needed |
| Data Fetching | **Supabase JS Client** | Supabase’s official client, SSR-friendly with RLS support |

---

## **3. Directory Structure**

```bash
/app
  /dashboard           # Protected routes under /dashboard
  /auth                # Public auth routes (sign in, sign up)
  /vehicles            # Vehicle-related views
  /alerts              # Alert management UI
  /users               # User management
  layout.tsx           # Main layout shell with navbar/sidebar
/lib
  supabase.ts          # Supabase client singleton
  auth.ts              # Helper functions (e.g., `getUser`)
/components
  /ui                  # Reusable shadcn components
  /charts              # Vehicle trends or fraud data charts
  Sidebar.tsx
  Navbar.tsx
/types
  index.ts             # Commonly shared types
/utils
  formatters.ts        # Utility functions (dates, text, etc.)
/middleware.ts         # Middleware for route protection
```

---

## **4. Authentication Flow**

- Uses **Supabase Auth (JWT)**.
- Auth state tracked via `supabase.auth.getUser()` and persisted client-side.
- Protected routes under `/dashboard/*` guarded via `middleware.ts`.

```tsx
// /middleware.ts
import { createMiddlewareSupabaseClient } from '@supabase/auth-helpers/nextjs'

export async function middleware(req) {
  const res = NextResponse.next()
  const supabase = createMiddlewareSupabaseClient({ req, res })
  const { data: { user } } = await supabase.auth.getUser()

  if (!user && req.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/auth/sign-in', req.url))
  }

  return res
}
```

---

## **5. Role-Based Access Control**

Supabase RLS will restrict access on DB-level (e.g., `vehicles`, `alerts`) using `user_roles` and `permissions`.

- UI checks will enforce allowed features using a `usePermissions()` hook.
- Admin-only actions (e.g., delete vehicle) will be conditionally rendered.

```tsx
const { hasPermission } = usePermissions()
if (hasPermission('vehicle:delete')) {
  // Show delete button
}
```

---

## **6. Key Pages and Features**

### **Dashboard Overview**

- KPIs: Total vehicles, alerts, recent ownership transfers
- Fraud Stats: Chart of suspicious activities
- Recent Activity: Data submissions (audit logs)

### **Vehicles Page**

- VIN search
- List with filters (make, year, etc.)
- View full vehicle history (owners, plates, colors, maintenance)

### **Alerts Page**

- List of alerts with status filters
- Map view of reported fraud using PostGIS location data
- Confirmation workflows (admin roles)

### **Users Page**

- Role assignment (user_roles table)
- Activity status (`last_active_at`)
- Admin-only access to modify roles

### **Audit Logs Page**

- Comprehensive view of all `data_submissions` entries
- Detailed view to compare `old_data` vs `new_data` for updates
- Advanced filtering by table name, action type (create, update, delete), and user
- Search functionality for record ID or table name
- Pagination with first/last page navigation
- Export filtered logs to CSV format
- Color-coded action types for better visual distinction
- Admin-only access enforced through permissions

---

## **7. Supabase DB & Security**

- **Row-Level Security (RLS):** Enforced for sensitive tables (`vehicles`, `alerts`, etc.).
- **Policies:** Defined per table per action using role check joins.
- **PostGIS Enabled:** For location filtering and fraud heatmaps.
- **Audit Trail:** `data_submissions` tracks all CRUD operations.

---

## **8. Deployment**

| Environment | Platform | Notes |
| --- | --- | --- |
| Preview | Vercel | Preview deployments per PR |
| Production | Vercel + Supabase | CI/CD with protected branches |

---

## **9. Dev Tools**

- **shadcn CLI** for component scaffolding
- **@supabase/cli** for local DB migrations (if needed)
- **Prettier + ESLint** for consistent formatting
- **Jest + Testing Library** (optional) for frontend tests

---

## **10. Future Enhancements**

- Real-time updates via `supabase.channel()`
- Push Notification Settings Page
- Partner API to verify VINs externally
