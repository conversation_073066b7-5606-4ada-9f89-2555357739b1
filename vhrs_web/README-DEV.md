# VHRS Web Application - Developer Documentation

This document provides technical details and implementation notes for developers working on the VHRS (Vehicle History Record System) web application.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Tech Stack](#tech-stack)
3. [Development Setup](#development-setup)
4. [Authentication Implementation](#authentication-implementation)
5. [Project Structure](#project-structure)
6. [Role-Based Access Control](#role-based-access-control)
7. [Performance Optimization](#performance-optimization)
8. [Troubleshooting](#troubleshooting)
9. [Best Practices](#best-practices)
10. [Contribution Guidelines](#contribution-guidelines)
11. [Supabase Integration](#supabase-integration)

## Project Overview

The VHRS Web Application serves as the administrative dashboard for the Uganda Vehicle History System. It facilitates management of vehicles, users, alerts, and audit logs using Supabase's backend capabilities.

## Tech Stack

- **Frontend**: Next.js (App Router)
- **UI Components**: shadcn/ui + Tailwind CSS
- **Authentication & Backend**: Supabase (Auth, PostgreSQL, Storage)
- **State Management**: React Hooks + Context
- **Data Fetching**: Supabase JS Client

## Development Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd vhrs_web
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   Create a `.env.local` file in the root directory with the following variables:

   ```bash
   NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
   NEXT_PUBLIC_SUPABASE_ANON_KEY=<your-anon-key>
   ```

4. **Start the development server**

   ```bash
   npm run dev
   ```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Authentication Implementation

The authentication system uses Supabase Auth with custom session persistence to ensure proper functioning across both client-side and server-side (middleware) contexts.

### Session Persistence

We use a dual-storage approach to maintain authentication state:

1. **localStorage**: For client-side persistence
2. **Cookies**: For middleware access

This is implemented in the Supabase client configuration:

```typescript
// src/lib/supabase.ts
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storage: {
      getItem: (key) => {
        if (typeof window !== 'undefined') {
          return window.localStorage.getItem(key);
        }
        return null;
      },
      setItem: (key, value) => {
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, value);
          // Also set as cookie for middleware access
          document.cookie = `${key}=${value}; path=/; max-age=3600; SameSite=Lax`;
        }
      },
      removeItem: (key) => {
        if (typeof window !== 'undefined') {
          window.localStorage.removeItem(key);
          // Also remove cookie
          document.cookie = `${key}=; path=/; max-age=0; SameSite=Lax`;
        }
      },
    },
  }
});
```

### Sign-in Process

The sign-in process explicitly sets cookies to ensure middleware can access the authentication state:

```typescript
// src/hooks/useAuth.ts (signIn function)
const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (!error && data.session) {
    // Update local state immediately
    setSession(data.session);
    setUser(data.user);

    // Manually set cookies for middleware access
    document.cookie = `sb-access-token=${data.session.access_token}; path=/; max-age=3600; SameSite=Lax`;
    document.cookie = `sb-refresh-token=${data.session.refresh_token}; path=/; max-age=7776000; SameSite=Lax`;

    // Store auth data in localStorage as a backup
    localStorage.setItem('supabase-auth-token', JSON.stringify([data.session.access_token, data.session.refresh_token]));
  }

  return { data, error };
};
```

### Middleware Implementation

The middleware checks for authentication using both the Supabase session and direct cookie inspection:

```typescript
// src/middleware.ts
export async function middleware(request: NextRequest) {
  // Check for auth cookie directly
  const hasAuthCookie = request.cookies.has('sb-access-token') ||
                        request.cookies.has('sb-refresh-token') ||
                        request.cookies.has('supabase-auth-token');

  // Create a Supabase client with cookie support
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL as string,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        storage: {
          getItem: (key) => {
            const cookies = request.cookies.getAll();
            const cookie = cookies.find((c) => c.name === key);
            return cookie?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
    }
  );

  // Get the session from Supabase
  const { data: { session } } = await supabase.auth.getSession();

  const isAuthenticated = !!session || hasAuthCookie;

  // Handle route protection based on authentication status
  // ...
}
```

### Navigation After Authentication

For navigation after authentication state changes, we use the Next.js router:

```typescript
// After successful sign-in
router.push('/dashboard');
```

With proper session persistence configuration, the Next.js router works correctly for authentication-related navigation.

## Project Structure

The project follows a feature-based structure:

```bash
/src
  /app                  # Next.js App Router pages
    /auth               # Authentication pages
    /dashboard          # Protected dashboard pages
    /...                # Other page routes
  /components           # Reusable components
    /ui                 # shadcn UI components
    /layout             # Layout components (Sidebar, Navbar)
    /...                # Feature-specific components
  /hooks                # Custom React hooks
  /lib                  # Utility libraries
    supabase.ts         # Supabase client
    utils.ts            # Helper functions
  /types                # TypeScript type definitions
  middleware.ts         # Next.js middleware for route protection
```

## State Management

The application uses React's built-in state management with hooks and context:

- **useAuth**: Custom hook for authentication state and methods
- **React Context**: For sharing state between components when needed
- **Local Component State**: For component-specific state

## API Integration

Supabase JS Client is used for all data operations:

- **Authentication**: `supabase.auth.*`
- **Database**: `supabase.from('table_name').*`
- **Storage**: `supabase.storage.*`
- **Edge Functions**: `supabase.functions.invoke('function_name')`

## UI Components

The application uses shadcn/ui components with Tailwind CSS for styling:

- **Component Library**: shadcn/ui provides accessible, customizable components
- **Styling**: Tailwind CSS for utility-first styling
- **Theme**: Custom theme variables in `globals.css`

## Role-Based Access Control

The application implements role-based access control (RBAC) using Supabase's Row Level Security (RLS) policies and user roles.

### User Roles

The system has two default roles:

1. **admin**: Full system access
2. **user**: Basic access to view their own data

Admins can create custom agent roles with specific permissions. For example, a "data-agent" role could be created to allow users to access and modify vehicle records without having full admin privileges.

### Implementation

Role assignments are managed through the database:

- `roles` table: Defines available roles
- `user_roles` table: Maps users to roles
- RLS policies: Control data access based on user roles

### UI Authorization

The UI conditionally renders components based on the user's role:

```typescript
// Example of permission-based UI rendering
const { user } = useAuth();
const { hasPermission, isAdmin } = usePermissions();

return (
  <div>
    {isAdmin && <AdminPanel />}
    {hasPermission('vehicle:edit') && <VehicleControls />}
    {hasPermission('alert:view') && <AlertControls />}
    <CommonUserInterface />
  </div>
);
```

## Performance Optimization

### Code Splitting

Next.js automatically code-splits at the page level. For additional optimization:

- Use dynamic imports for large components
- Implement lazy loading for below-the-fold content

### Image Optimization

Use Next.js Image component for automatic optimization:

```typescript
import Image from 'next/image';

// Optimized image
<Image
  src="/path/to/image.jpg"
  width={500}
  height={300}
  alt="Description"
/>
```

### Memoization

Use React's memoization features for expensive computations:

- `useMemo` for computed values
- `useCallback` for functions passed to child components
- `React.memo` for components that render often with the same props

## Troubleshooting

### Authentication Issues

If you encounter authentication issues:

1. **Check Browser Storage**: Inspect localStorage and cookies to ensure tokens are being stored
2. **Check Middleware Logs**: Look for "Middleware - Auth cookies present" and "Middleware - Authentication status" logs
3. **Session Persistence**: Ensure the custom storage handlers in `supabase.ts` are working correctly
4. **Cookie Access**: Verify that cookies are being set with the correct path and expiry

### Common Issues

- **Middleware Not Detecting Authentication**: Make sure cookies are being set correctly and the middleware is checking for them
- **Redirect Loops**: Check the conditions in middleware to ensure they're not creating redirect loops
- **Session Not Persisting**: Verify that both localStorage and cookies are being used for session storage

### Debugging Tips

- Use browser developer tools to inspect network requests, localStorage, and cookies
- Add console logs in key authentication functions and middleware
- Check Supabase logs in the Supabase dashboard
- Verify environment variables are correctly set

## Best Practices

### Code Organization

- Follow feature-based organization for components and hooks
- Keep related functionality together
- Use consistent naming conventions

### State Management Practices

- Use local state for component-specific state
- Use context for shared state that doesn't change often
- Consider more robust state management (Redux, Zustand) only if needed

### Security

- Never store sensitive information in localStorage or client-side state
- Use environment variables for API keys and secrets
- Implement proper input validation on both client and server
- Use HTTPS in production
- Set appropriate cookie security options (HttpOnly, SameSite, Secure)

### Performance

- Minimize unnecessary re-renders
- Optimize images and assets
- Use pagination for large data sets
- Implement proper error boundaries

## Contribution Guidelines

1. **Branch Strategy**
   - `main`: Production-ready code
   - `develop`: Integration branch for features
   - Feature branches: `feature/feature-name`
   - Bug fixes: `fix/bug-description`

2. **Pull Request Process**
   - Create a PR against the `develop` branch
   - Include a description of changes
   - Reference any related issues
   - Ensure all tests pass
   - Request review from at least one team member

3. **Code Style**
   - Follow the project's ESLint and Prettier configuration
   - Write meaningful commit messages
   - Document complex logic and components

## Supabase Integration

### Local Development

For local development, the application connects to a local Supabase instance running at `http://127.0.0.1:54321`. To set up the local Supabase instance:

1. Navigate to the `vhrs_data` directory
2. Run the Supabase start script:

```bash
cd ../vhrs_data
./supabase.sh start
```

### Database Schema

The database schema includes:

- `profiles`: User profiles with contact information
- `roles`: User role definitions (admin, user, and custom roles created by admins)
- `user_roles`: Mapping between users and roles
- `permissions`: Granular permissions for actions
- `role_permissions`: Mapping between roles and permissions
- `vehicles`: Vehicle information
- `history_report_requests`: Vehicle history report requests
- `alerts`: System alerts and notifications

### Edge Functions

The application uses Supabase Edge Functions for server-side logic:

- `generate-vehicle-history`: Generates vehicle history records
- `process-alerts`: Processes and sends alerts
- `sync-users`: Synchronizes auth users with profiles

To invoke an edge function:

```typescript
const { data, error } = await supabase.functions.invoke('function-name', {
  body: { param1: 'value1', param2: 'value2' }
});
```

### Realtime Subscriptions

The application uses Supabase Realtime for live updates:

```typescript
// Subscribe to changes in the alerts table
const subscription = supabase
  .channel('table-db-changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'alerts',
    },
    (payload) => {
      console.log('Change received!', payload);
      // Update UI based on the change
    }
  )
  .subscribe();

// Cleanup on component unmount
return () => {
  supabase.removeChannel(subscription);
};
```

## Conclusion

This developer documentation provides a comprehensive overview of the VHRS web application's technical implementation. By following these guidelines and best practices, you can effectively contribute to and maintain the application.

For any questions or issues not covered in this documentation, please reach out to the development team or create an issue in the project repository.
