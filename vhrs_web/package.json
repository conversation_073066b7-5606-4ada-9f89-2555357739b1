{"name": "vhrs_web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@lottiefiles/dotlottie-react": "^0.13.5", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.2.1", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.0.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.21.3", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.12.1", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.34", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.8", "eslint": "^9", "eslint-config-next": "15.3.1", "lucide-react": "^0.507.0", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}