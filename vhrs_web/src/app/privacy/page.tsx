'use client';

import { LandingFooter, LandingHeader } from '@/components/landing-page';
import { Card, CardContent } from '@/components/ui/card';
import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

export default function PrivacyPolicyPage() {
  const { settings } = useSystemSettings();
  const [currentDate, setCurrentDate] = useState('');

  useEffect(() => {
    setCurrentDate(new Date().toLocaleDateString());
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <LandingHeader />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary/5 to-primary/10 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Privacy Policy
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Your privacy is important to us. This policy explains how we collect, use, and protect your information.
              </p>
              <p className="text-sm text-gray-500 mt-4">
                Last updated: {currentDate || 'Loading...'}
              </p>
            </motion.div>
          </div>
        </section>

        {/* Privacy Policy Content */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card>
                <CardContent className="p-8 space-y-8">

                  <PrivacySection
                    title="1. Information We Collect"
                    content={[
                      "Personal Information: When you create an account or use our services, we may collect personal information such as your name, email address, phone number, and contact details.",
                      "Vehicle Information: We collect vehicle identification numbers (VINs), license plate numbers, chassis numbers, and related vehicle data that you provide when requesting vehicle history reports.",
                      "Usage Data: We automatically collect information about how you use our website and services, including your IP address, browser type, pages visited, and time spent on our site.",
                      "Cookies and Tracking: We use cookies and similar technologies to enhance your experience and analyze website usage."
                    ]}
                  />

                  <PrivacySection
                    title="2. How We Use Your Information"
                    content={[
                      "Service Provision: To provide vehicle history reports and related services you request.",
                      "Account Management: To create and manage your user account and provide customer support.",
                      "Communication: To send you service-related notifications, updates, and respond to your inquiries.",
                      "Improvement: To analyze usage patterns and improve our services, website functionality, and user experience.",
                      "Legal Compliance: To comply with applicable laws, regulations, and legal processes."
                    ]}
                  />

                  <PrivacySection
                    title="3. Information Sharing and Disclosure"
                    content={[
                      "We do not sell, trade, or rent your personal information to third parties.",
                      "Service Providers: We may share information with trusted third-party service providers who assist us in operating our website and providing services, under strict confidentiality agreements.",
                      "Legal Requirements: We may disclose information when required by law, court order, or to protect our rights and safety.",
                      "Business Transfers: In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the transaction."
                    ]}
                  />

                  <PrivacySection
                    title="4. Data Security"
                    content={[
                      "We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.",
                      "We use encryption, secure servers, and regular security assessments to safeguard your data.",
                      "However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security."
                    ]}
                  />

                  <PrivacySection
                    title="5. Data Retention"
                    content={[
                      "We retain your personal information for as long as necessary to provide our services and fulfill the purposes outlined in this policy.",
                      "Account information is retained until you request account deletion or we determine it's no longer needed.",
                      "Vehicle history data may be retained for longer periods as required by law or for legitimate business purposes."
                    ]}
                  />

                  <PrivacySection
                    title="6. Your Rights and Choices"
                    content={[
                      "Access: You can request access to the personal information we hold about you.",
                      "Correction: You can request correction of inaccurate or incomplete personal information.",
                      "Deletion: You can request deletion of your personal information, subject to legal and business requirements.",
                      "Opt-out: You can opt out of marketing communications at any time.",
                      "Data Portability: You can request a copy of your data in a portable format."
                    ]}
                  />

                  <PrivacySection
                    title="7. Cookies and Tracking Technologies"
                    content={[
                      "We use cookies to enhance your browsing experience, remember your preferences, and analyze website traffic.",
                      "You can control cookie settings through your browser preferences.",
                      "Some features of our website may not function properly if you disable cookies."
                    ]}
                  />

                  <PrivacySection
                    title="8. Third-Party Links"
                    content={[
                      "Our website may contain links to third-party websites or services.",
                      "We are not responsible for the privacy practices or content of these third-party sites.",
                      "We encourage you to review the privacy policies of any third-party sites you visit."
                    ]}
                  />

                  <PrivacySection
                    title="9. Children's Privacy"
                    content={[
                      "Our services are not intended for children under the age of 13.",
                      "We do not knowingly collect personal information from children under 13.",
                      "If we become aware that we have collected information from a child under 13, we will take steps to delete such information."
                    ]}
                  />

                  <PrivacySection
                    title="10. Changes to This Policy"
                    content={[
                      "We may update this privacy policy from time to time to reflect changes in our practices or applicable laws.",
                      "We will notify you of any material changes by posting the updated policy on our website.",
                      "Your continued use of our services after any changes constitutes acceptance of the updated policy."
                    ]}
                  />

                  <PrivacySection
                    title="11. Contact Us"
                    content={[
                      `If you have any questions about this privacy policy or our data practices, please contact us:`,
                      settings.contact_email ? `Email: ${settings.contact_email}` : '',
                      settings.support_phone ? `Phone: ${settings.support_phone}` : '',
                      "Address: Kampala, Uganda"
                    ].filter(Boolean)}
                  />

                </CardContent>
              </Card>
            </motion.div>
          </div>
        </section>
      </main>

      <LandingFooter />
    </div>
  );
}

interface PrivacySectionProps {
  title: string;
  content: string[];
}

function PrivacySection({ title, content }: PrivacySectionProps) {
  return (
    <div>
      <h2 className="text-xl font-bold text-gray-900 mb-4">{title}</h2>
      <div className="space-y-3">
        {content.map((paragraph, index) => (
          <p key={index} className="text-gray-600 leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    </div>
  );
}
