'use client';

import { FAQSection, FeaturesSection, HeroSection, LandingFooter, LandingHeader, TestimonialsSection, VehicleHistoryCheckCard, WhyChooseUsSection } from '@/components/landing-page';
import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const { settings } = useSystemSettings();

  // Auto-redirect logged in users to dashboard
  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  // If still loading or user is logged in (and being redirected), show loading state
  if (loading || user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold">{settings.system_name}</h1>
          <p className="mt-2 text-neutral-500">Redirecting...</p>
        </div>
      </div>
    );
  }

  // For non-logged in users, show the landing page
  return (
    <div className="flex flex-col min-h-screen">
      <LandingHeader />

      <main className="flex-grow">
        <HeroSection />
        <VehicleHistoryCheckCard />

        <div id="learn-more" className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
          <FeaturesSection />
          <WhyChooseUsSection />
          <TestimonialsSection />
          <FAQSection />
        </div>
      </main>

      <LandingFooter />
    </div>
  );
}
