@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Inter, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --radius: 0.75rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.75rem;
  --background: oklch(1.00 0 0);
  --foreground: oklch(0.26 0 0);
  --card: oklch(1.00 0 0);
  --card-foreground: oklch(0.26 0 0);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.26 0 0);
  --primary: oklch(0.54 0.09 194.77);
  --primary-foreground: oklch(0.99 0 0);
  --secondary: oklch(0.98 0 0);
  --secondary-foreground: oklch(0.54 0.09 194.77);
  --muted: oklch(0.98 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.98 0 0);
  --accent-foreground: oklch(0.33 0 0);
  --destructive: oklch(0.62 0.21 25.77);
  --border: oklch(0.94 0 0);
  --input: oklch(0.94 0 0);
  --ring: oklch(0.77 0 0);
  --chart-1: oklch(0.82 0.13 84.49);
  --chart-2: oklch(0.80 0.11 203.60);
  --chart-3: oklch(0.42 0.17 266.78);
  --chart-4: oklch(0.92 0.08 125.58);
  --chart-5: oklch(0.92 0.10 116.19);
  --sidebar: oklch(0.99 0 0);
  --sidebar-foreground: oklch(0.26 0 0);
  --sidebar-primary: oklch(0.33 0 0);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.98 0 0);
  --sidebar-accent-foreground: oklch(0.33 0 0);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0.77 0 0);
  --destructive-foreground: oklch(1.00 0 0);
  --font-sans: Inter, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.14 0.00 285.82);
  --foreground: oklch(0.98 0 0);
  --card: oklch(0.14 0.00 285.82);
  --card-foreground: oklch(0.98 0 0);
  --popover: oklch(0.14 0.00 285.82);
  --popover-foreground: oklch(0.98 0 0);
  --primary: oklch(0.98 0 0);
  --primary-foreground: oklch(0.21 0.01 285.88);
  --secondary: oklch(0.27 0.01 286.03);
  --secondary-foreground: oklch(0.98 0 0);
  --muted: oklch(0.27 0.01 286.03);
  --muted-foreground: oklch(0.71 0.01 286.07);
  --accent: oklch(0.27 0.01 286.03);
  --accent-foreground: oklch(0.98 0 0);
  --destructive: oklch(0.40 0.13 25.72);
  --border: oklch(0.27 0.01 286.03);
  --input: oklch(0.27 0.01 286.03);
  --ring: oklch(0.87 0.01 286.29);
  --chart-1: oklch(0.53 0.19 262.13);
  --chart-2: oklch(0.70 0.13 165.46);
  --chart-3: oklch(0.72 0.15 60.63);
  --chart-4: oklch(0.62 0.20 312.73);
  --chart-5: oklch(0.61 0.21 6.39);
  --sidebar: oklch(0.21 0.01 285.88);
  --sidebar-foreground: oklch(0.97 0.00 286.38);
  --sidebar-primary: oklch(0.49 0.22 264.39);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.27 0.01 286.03);
  --sidebar-accent-foreground: oklch(0.97 0.00 286.38);
  --sidebar-border: oklch(0.27 0.01 286.03);
  --sidebar-ring: oklch(0.87 0.01 286.29);
  --destructive-foreground: oklch(0.97 0.01 17.38);
  --radius: 0.75rem;
  --font-sans: Inter, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}