import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Profile from './page';
import { useAuth } from '@/hooks/useAuth';
import { useUserRoles } from '@/hooks/useUserRoles';
import { supabase } from '@/lib/supabase';

// Mock the hooks and supabase client
jest.mock('@/hooks/useAuth');
jest.mock('@/hooks/useUserRoles');
jest.mock('@/lib/supabase');
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

describe('Profile Component', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Test User',
      preferences: {
        emailNotifications: true,
        pushNotifications: false,
        alertNotifications: true,
        historyNotifications: true,
      },
    },
  };

  const mockUpdateProfile = jest.fn();
  const mockUpdatePassword = jest.fn();
  const mockSignOut = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      updateProfile: mockUpdateProfile,
      updatePassword: mockUpdatePassword,
      signOut: mockSignOut,
    });
    
    (useUserRoles as jest.Mock).mockReturnValue({
      roles: ['user'],
      loading: false,
      hasPermission: jest.fn().mockReturnValue(false),
    });
    
    // Mock Supabase responses
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue({
        data: [],
        error: null,
      }),
    });

    (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });

    (supabase.auth.updateUser as jest.Mock).mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
  });

  test('renders profile information', async () => {
    render(<Profile />);
    
    await waitFor(() => {
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
      expect(screen.getByText('Profile Information')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test User')).toBeInTheDocument();
    });
  });

  test('updates profile information', async () => {
    render(<Profile />);
    
    // Change the full name
    const fullNameInput = screen.getByLabelText(/Full Name/i);
    fireEvent.change(fullNameInput, { target: { value: 'Updated Name' } });
    
    // Submit the form
    const updateButton = screen.getByText('Update Profile');
    fireEvent.click(updateButton);
    
    // Check that the update function was called
    await waitFor(() => {
      expect(mockUpdateProfile).toHaveBeenCalledWith('Updated Name');
    });
  });

  test('updates password', async () => {
    render(<Profile />);
    
    // Navigate to security tab
    fireEvent.click(screen.getByText('Security'));
    
    // Fill in password fields
    fireEvent.change(screen.getByLabelText(/Current Password/i), { target: { value: 'oldpassword' } });
    fireEvent.change(screen.getByLabelText(/New Password/i), { target: { value: 'newpassword' } });
    fireEvent.change(screen.getByLabelText(/Confirm New Password/i), { target: { value: 'newpassword' } });
    
    // Submit the form
    const changePasswordButton = screen.getByText('Change Password');
    fireEvent.click(changePasswordButton);
    
    // Check that the update function was called
    await waitFor(() => {
      expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'oldpassword',
      });
      expect(mockUpdatePassword).toHaveBeenCalledWith('newpassword');
    });
  });

  test('updates notification preferences', async () => {
    render(<Profile />);
    
    // Navigate to notifications tab
    fireEvent.click(screen.getByText('Notifications'));
    
    // Toggle push notifications
    const pushNotificationsSwitch = screen.getByLabelText(/Push Notifications/i);
    fireEvent.click(pushNotificationsSwitch);
    
    // Submit the form
    const savePreferencesButton = screen.getByText('Save Preferences');
    fireEvent.click(savePreferencesButton);
    
    // Check that the update function was called
    await waitFor(() => {
      expect(supabase.auth.updateUser).toHaveBeenCalledWith({
        data: {
          preferences: {
            emailNotifications: true,
            pushNotifications: true,
            alertNotifications: true,
            historyNotifications: true,
          },
        },
      });
    });
  });

  test('shows activity tab', async () => {
    // Mock activity data
    (supabase.from as jest.Mock).mockReturnValueOnce({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue({
        data: [
          { id: '1', created_at: new Date().toISOString(), alert_type: 'Test Alert', status: 'pending' },
        ],
        error: null,
      }),
    }).mockReturnValueOnce({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue({
        data: [
          { id: '2', created_at: new Date().toISOString(), status: 'completed' },
        ],
        error: null,
      }),
    });
    
    render(<Profile />);
    
    // Navigate to activity tab
    fireEvent.click(screen.getByText('Activity'));
    
    // Check that activity is displayed
    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
      expect(screen.getByText(/Refresh/i)).toBeInTheDocument();
    });
  });
});
