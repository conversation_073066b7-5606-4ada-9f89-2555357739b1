'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import { zodResolver } from '@hookform/resolvers/zod';
import { Bell, Mail, Phone } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

// Define the schema for notification preferences
const notificationPreferencesSchema = z.object({
  email_alerts: z.boolean().default(true),
  email_history_requests: z.boolean().default(true),
  email_system_updates: z.boolean().default(true),

  push_alerts: z.boolean().default(true),
  push_history_requests: z.boolean().default(true),
  push_system_updates: z.boolean().default(false),

  sms_alerts: z.boolean().default(false),
  sms_history_requests: z.boolean().default(false),
  sms_system_updates: z.boolean().default(false),
});

type NotificationPreferences = z.infer<typeof notificationPreferencesSchema>;

export default function NotificationsPage() {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('email');

  // Initialize form with default values
  const form = useForm<NotificationPreferences>({
    resolver: zodResolver(notificationPreferencesSchema),
    defaultValues: {
      email_alerts: true,
      email_history_requests: true,
      email_system_updates: true,

      push_alerts: true,
      push_history_requests: true,
      push_system_updates: false,

      sms_alerts: false,
      sms_history_requests: false,
      sms_system_updates: false,
    },
  });

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    setLoading(true);
    try {
      // Get current user
      const { data: { user: currentUser } } = await supabase.auth.getUser();

      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      setUser(currentUser);

      // Get notification preferences from user metadata
      const preferences = currentUser.user_metadata?.notification_preferences || {};

      // Update form values with preferences from metadata
      form.reset({
        email_alerts: preferences.email_alerts !== false,
        email_history_requests: preferences.email_history_requests !== false,
        email_system_updates: preferences.email_system_updates !== false,

        push_alerts: preferences.push_alerts !== false,
        push_history_requests: preferences.push_history_requests !== false,
        push_system_updates: preferences.push_system_updates === true,

        sms_alerts: preferences.sms_alerts === true,
        sms_history_requests: preferences.sms_history_requests === true,
        sms_system_updates: preferences.sms_system_updates === true,
      });
    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.error('Failed to load notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (values: NotificationPreferences) => {
    if (!user) return;

    try {
      // Update user metadata with notification preferences
      const { error } = await supabase.auth.updateUser({
        data: {
          notification_preferences: values
        }
      });

      if (error) throw error;

      toast.success('Notification preferences updated successfully');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast.error('Failed to update notification preferences');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Notification Preferences</h1>
          <p className="text-neutral-500">
            Manage how you receive notifications from the system
          </p>
        </div>
        <Link href="/dashboard/profile">
          <Button variant="outline">Back to Profile</Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Notification Settings</CardTitle>
          <CardDescription>
            Choose which notifications you want to receive and how you want to receive them
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    <span>Email</span>
                  </TabsTrigger>
                  <TabsTrigger value="push" className="flex items-center gap-2">
                    <Bell className="h-4 w-4" />
                    <span>Push</span>
                  </TabsTrigger>
                  <TabsTrigger value="sms" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    <span>SMS</span>
                  </TabsTrigger>
                </TabsList>

                {/* Email Notifications */}
                <TabsContent value="email" className="space-y-4 mt-4">
                  <FormField
                    control={form.control}
                    name="email_alerts"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Alert Notifications</FormLabel>
                          <FormDescription>
                            Receive email notifications about new alerts and status changes
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email_history_requests"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">History Record Requests</FormLabel>
                          <FormDescription>
                            Receive email notifications about your history record requests
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email_system_updates"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">System Updates</FormLabel>
                          <FormDescription>
                            Receive email notifications about system updates and new features
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {/* Push Notifications */}
                <TabsContent value="push" className="space-y-4 mt-4">
                  <FormField
                    control={form.control}
                    name="push_alerts"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Alert Notifications</FormLabel>
                          <FormDescription>
                            Receive push notifications about new alerts and status changes
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="push_history_requests"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">History Record Requests</FormLabel>
                          <FormDescription>
                            Receive push notifications about your history record requests
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="push_system_updates"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">System Updates</FormLabel>
                          <FormDescription>
                            Receive push notifications about system updates and new features
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {/* SMS Notifications */}
                <TabsContent value="sms" className="space-y-4 mt-4">
                  <FormField
                    control={form.control}
                    name="sms_alerts"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Alert Notifications</FormLabel>
                          <FormDescription>
                            Receive SMS notifications about new alerts and status changes
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="sms_history_requests"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">History Record Requests</FormLabel>
                          <FormDescription>
                            Receive SMS notifications about your history record requests
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="sms_system_updates"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">System Updates</FormLabel>
                          <FormDescription>
                            Receive SMS notifications about system updates and new features
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </Tabs>

              <CardFooter className="px-0 pt-4">
                <Button type="submit" disabled={loading}>
                  {loading ? 'Loading...' : 'Save Preferences'}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
