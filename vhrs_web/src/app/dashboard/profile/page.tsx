'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/hooks/useAuth';
import { useUserRoles } from '@/hooks/useUserRoles';
import { supabase } from '@/lib/supabase';
import { AlertTriangle, Bell, Clock, FileText, Lock, Shield, User as UserIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function Profile() {
  const { user, updateProfile, updatePassword, signOut } = useAuth();
  const { roles, loading: rolesLoading } = useUserRoles();

  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Notification preferences
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(false);
  const [alertNotifications, setAlertNotifications] = useState(true);
  const [historyNotifications, setHistoryNotifications] = useState(true);

  // Activity data
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [activityLoading, setActivityLoading] = useState(true);

  const [loadingProfile, setLoadingProfile] = useState(false);
  const [loadingPassword, setLoadingPassword] = useState(false);
  const [loadingPreferences, setLoadingPreferences] = useState(false);

  useEffect(() => {
    if (user) {
      setEmail(user.email || '');
      setFullName(user.user_metadata?.full_name || '');

      // Load user notification preferences from metadata or set defaults
      const preferences = user.user_metadata?.notification_preferences || {};
      setEmailNotifications(preferences.email_alerts !== false); // Default to true
      setPushNotifications(preferences.push_alerts === true); // Default to false
      setAlertNotifications(preferences.email_alerts !== false); // Default to true
      setHistoryNotifications(preferences.email_history_requests !== false); // Default to true

      // Fetch user activity
      fetchUserActivity();
    }
  }, [user]);

  const fetchUserActivity = async () => {
    if (!user) return;

    setActivityLoading(true);
    try {
      // Note: In a real application, we would use a server endpoint to get auth events
      // For this demo, we'll create mock login events
      const mockLoginEvents = [
        {
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
          ip_address: '***********',
        },
        {
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
          ip_address: '***********',
        },
      ];

      const authData = { events: mockLoginEvents };

      // Get recent actions (alerts, history requests)
      const [alertsRes, requestsRes] = await Promise.all([
        supabase
          .from('alerts')
          .select('id, created_at, alert_type, status')
          .eq('reported_by', user.id)
          .order('created_at', { ascending: false })
          .limit(3),
        supabase
          .from('history_report_requests')
          .select('id, created_at, status')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(3),
      ]);

      // Combine and format activity data
      const loginEvents = (authData?.events || []).map(event => ({
        type: 'login',
        date: event.created_at,
        details: `Login from ${event.ip_address || 'unknown location'}`,
      }));

      const alertEvents = (alertsRes.data || []).map(alert => ({
        type: 'alert',
        date: alert.created_at,
        details: `Submitted ${alert.alert_type} alert (${alert.status})`,
        id: alert.id,
      }));

      const requestEvents = (requestsRes.data || []).map(request => ({
        type: 'request',
        date: request.created_at,
        details: `Requested vehicle history record (${request.status})`,
        id: request.id,
      }));

      // Combine all events, sort by date (newest first), and limit to 10
      const allActivity = [...loginEvents, ...alertEvents, ...requestEvents]
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 10);

      setRecentActivity(allActivity);
    } catch (error) {
      console.error('Error fetching user activity:', error);
    } finally {
      setActivityLoading(false);
    }
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!fullName) {
      toast.error('Please enter your full name');
      return;
    }

    setLoadingProfile(true);

    try {
      const { error } = await updateProfile(fullName);

      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Profile updated successfully');
      }
    } catch (error) {
      console.error('Update profile error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoadingProfile(false);
    }
  };

  const handleUpdateNotificationPreferences = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoadingPreferences(true);

    try {
      // Get current notification preferences from user metadata
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      const currentPreferences = currentUser?.user_metadata?.notification_preferences || {};

      // Update user metadata with notification preferences
      const { data, error } = await supabase.auth.updateUser({
        data: {
          notification_preferences: {
            ...currentPreferences,
            // Update the basic preferences
            email_alerts: emailNotifications,
            push_alerts: pushNotifications,
            email_history_requests: historyNotifications,
            push_history_requests: historyNotifications,
          },
        },
      });

      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Notification preferences updated successfully');
      }
    } catch (error) {
      console.error('Update preferences error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoadingPreferences(false);
    }
  };

  const handleUpdatePassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentPassword || !newPassword || !confirmPassword) {
      toast.error('Please fill in all password fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    setLoadingPassword(true);

    try {
      // First verify the current password by attempting to sign in
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password: currentPassword,
      });

      if (signInError) {
        toast.error('Current password is incorrect');
        setLoadingPassword(false);
        return;
      }

      // Then update the password
      const { error } = await updatePassword(newPassword);

      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Password updated successfully');
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch (error) {
      console.error('Update password error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoadingPassword(false);
    }
  };

  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading profile...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-8 text-3xl font-bold">Profile Settings</h1>

      <Tabs defaultValue="profile" className="w-full mb-8">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <UserIcon className="h-4 w-4" />
            <span className="hidden sm:inline">Profile</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span className="hidden sm:inline">Activity</span>
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>Update your account details</CardDescription>
            </CardHeader>
            <form onSubmit={handleUpdateProfile}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    disabled
                    className="bg-neutral-100"
                  />
                  <p className="text-xs text-neutral-500">
                    Email cannot be changed
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    required
                  />
                </div>
                {/* Only show roles for admin and data_agent users */}
                {roles.includes('admin') || roles.includes('data_agent') ? (
                  <div className="space-y-2">
                    <Label>User Roles</Label>
                    <div className="rounded-md bg-neutral-100 p-3">
                      {rolesLoading ? (
                        <p className="text-sm text-neutral-500">Loading roles...</p>
                      ) : roles.length > 0 ? (
                        <div className="flex flex-wrap gap-2">
                          {roles.map((role) => (
                            <span
                              key={role}
                              className="rounded-full bg-neutral-200 px-3 py-1 text-xs font-medium text-neutral-800"
                            >
                              {role}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-neutral-500">No roles assigned</p>
                      )}
                    </div>
                  </div>
                ) : null}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={loadingProfile}>
                  {loadingProfile ? 'Updating...' : 'Update Profile'}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="mt-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>Update your password</CardDescription>
              </CardHeader>
              <form onSubmit={handleUpdatePassword}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={loadingPassword}>
                    {loadingPassword ? 'Updating...' : 'Change Password'}
                  </Button>
                </CardFooter>
              </form>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Account Security</CardTitle>
                <CardDescription>Manage your account security settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">
                      Add an extra layer of security to your account
                    </p>
                  </div>
                  <Button variant="outline" disabled>Coming Soon</Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Active Sessions</Label>
                    <p className="text-sm text-muted-foreground">
                      Manage devices where you're currently logged in
                    </p>
                  </div>
                  <Button variant="outline" disabled>Coming Soon</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>Manage how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configure detailed notification preferences including email, push, and SMS notifications for different types of events.
                </p>

                <div className="flex justify-center">
                  <Button asChild>
                    <a href="/dashboard/profile/notifications" className="flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      <span>Manage Notification Preferences</span>
                    </a>
                  </Button>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="space-y-1">
                  <h3 className="text-sm font-medium">Quick Settings</h3>
                  <p className="text-sm text-muted-foreground">
                    Quickly toggle basic notification settings
                  </p>
                </div>

                <form onSubmit={handleUpdateNotificationPreferences} className="mt-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="emailNotifications">Email Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive notifications via email
                        </p>
                      </div>
                      <Switch
                        id="emailNotifications"
                        checked={emailNotifications}
                        onCheckedChange={setEmailNotifications}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="pushNotifications">Push Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive notifications on your device
                        </p>
                      </div>
                      <Switch
                        id="pushNotifications"
                        checked={pushNotifications}
                        onCheckedChange={setPushNotifications}
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <Button type="submit" variant="outline" disabled={loadingPreferences} className="w-full">
                      {loadingPreferences ? 'Saving...' : 'Save Quick Settings'}
                    </Button>
                  </div>
                </form>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Your recent account activity</CardDescription>
            </CardHeader>
            <CardContent>
              {activityLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-4">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-[250px]" />
                        <Skeleton className="h-4 w-[200px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : recentActivity.length > 0 ? (
                <div className="space-y-6">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="rounded-full bg-neutral-100 p-2">
                        {activity.type === 'login' ? (
                          <Lock className="h-4 w-4 text-neutral-500" />
                        ) : activity.type === 'alert' ? (
                          <AlertTriangle className="h-4 w-4 text-neutral-500" />
                        ) : (
                          <FileText className="h-4 w-4 text-neutral-500" />
                        )}
                      </div>
                      <div>
                        <p className="text-sm font-medium">{activity.details}</p>
                        <p className="text-xs text-neutral-500">
                          {new Date(activity.date).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-neutral-500">No recent activity found</p>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <p className="text-sm text-neutral-500">
                Activity is updated in real-time
              </p>
              <Button variant="outline" size="sm" onClick={fetchUserActivity}>
                Refresh
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
