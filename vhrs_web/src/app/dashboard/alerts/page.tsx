'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { Check } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

type Alert = {
  id: string;
  license_plate: string | null;
  description: string;
  status: string;
  created_at: string;
  alert_type: {
    name: string;
  } | null;
  vehicle: {
    make: string;
    model: string;
    year: number;
  } | null;
};

export default function AlertsPage() {
  const { user } = useAuth();
  const { isAdmin, hasPermission, userId } = usePermissions();
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showMyAlertsOnly, setShowMyAlertsOnly] = useState(false);
  const router = useRouter();

  // Define fetchAlerts inside useEffect to properly capture dependencies
  useEffect(() => {
    const fetchAlerts = async () => {
      if (!user) return;

      setLoading(true);
      try {
        let query = supabase
          .from('alerts')
          .select(`
            id,
            license_plate,
            description,
            status,
            created_at,
            alert_type:alert_type_id(name),
            vehicle:vehicle_id(make, model, year)
          `)
          .order('created_at', { ascending: false });

        // Apply status filter if not 'all'
        if (statusFilter !== 'all') {
          query = query.eq('status', statusFilter);
        }

        // Filter by user if needed
        if (!isAdmin && !hasPermission('alert:view')) {
          // Regular users only see their own alerts
          query = query.eq('created_by', user.id);
        } else if (isAdmin || hasPermission('alert:view')) {
          // Admin or users with alert:view permission with "My Alerts" filter
          if (showMyAlertsOnly && userId) {
            query = query.eq('created_by', userId);
          }
          // When showMyAlertsOnly is false, no filter is applied so all alerts are shown
        }

        const { data, error } = await query;

        if (error) {
          throw error;
        }
        setAlerts(data || []);
      } catch (error) {
        console.error('Error fetching alerts:', error);
        toast.error('Failed to load alerts');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchAlerts();
    }
  }, [user, statusFilter, showMyAlertsOnly, userId, isAdmin, hasPermission]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled client-side via the filteredAlerts
  };

  const handleToggleMyAlerts = () => {
    // Toggle the state which will trigger the useEffect to refetch alerts
    setShowMyAlertsOnly(!showMyAlertsOnly);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'confirmed':
        return 'success';
      case 'rejected':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const filteredAlerts = searchTerm
    ? alerts.filter(
        (alert) =>
          alert.license_plate?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          alert.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          alert.alert_type?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (alert.vehicle &&
            `${alert.vehicle.make} ${alert.vehicle.model}`.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : alerts;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Alerts</h1>
        <Link href="/dashboard/alerts/new">
          <Button>Submit New Alert</Button>
        </Link>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Filter Alerts</CardTitle>
          <CardDescription>Use the filters below to find specific alerts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <form onSubmit={handleSearch} className="flex space-x-2 flex-1">
                <Input
                  placeholder="Search by plate, description, or type"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit">Search</Button>
              </form>
            </div>
            <div className="flex flex-row items-center justify-between gap-2 sm:gap-4">
              <div className="flex items-center gap-2 min-w-0">
                <span className="text-sm font-medium hidden sm:inline">Status:</span>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full min-w-[120px] max-w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {(isAdmin || hasPermission('alert:view')) && (
                <Button
                  variant={showMyAlertsOnly ? "default" : "outline"}
                  onClick={handleToggleMyAlerts}
                  className="flex items-center gap-2 whitespace-nowrap"
                  size="sm"
                >
                  {showMyAlertsOnly && <Check className="h-4 w-4" />}
                  My Alerts
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredAlerts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredAlerts.map((alert) => (
            <Card key={alert.id} className="hover:shadow-md transition-shadow h-full">
              <CardContent className="p-6">
                <div className="flex flex-col h-full">
                  <div className="space-y-1 mb-4 flex-grow">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium">
                        {alert.alert_type?.name
                          ? alert.alert_type.name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
                          : 'Unknown Alert Type'}
                      </h3>
                      <Badge variant={getStatusBadgeVariant(alert.status)}>
                        {alert.status.charAt(0).toUpperCase() + alert.status.slice(1)}
                      </Badge>
                    </div>
                    <p className="text-sm text-neutral-500 truncate max-w-md">{alert.description}</p>
                    <div className="flex flex-wrap gap-2 text-sm text-neutral-500">
                      <span>Reported: {formatDate(alert.created_at)}</span>
                      {alert.license_plate && <span>Plate: {alert.license_plate}</span>}
                      {alert.vehicle && (
                        <span>
                          Vehicle: {alert.vehicle.make} {alert.vehicle.model} ({alert.vehicle.year})
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Link href={`/dashboard/alerts/${alert.id}`}>
                      <Button variant="outline">View Details</Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <h3 className="text-lg font-medium text-neutral-900 mb-2">No alerts found</h3>
          <p className="text-neutral-500 mb-6">
            {searchTerm || statusFilter !== 'all'
              ? 'Try changing your search or filter criteria'
              : 'There are no alerts in the system yet'}
          </p>
          <Link href="/dashboard/alerts/new">
            <Button>Submit New Alert</Button>
          </Link>
        </div>
      )}
    </div>
  );
}
