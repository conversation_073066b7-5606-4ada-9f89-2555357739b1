'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

type AlertMedia = {
  id: string;
  media_url: string;
  media_type: string;
  description: string;
  details: any;
  uploaded_at: string;
};

type Alert = {
  id: string;
  vehicle_id: string | null;
  license_plate: string | null;
  alert_type_id: string;
  alert_type?: {
    name: string;
    description: string;
  };
  confidence_score: number | null;
  location: any | null;
  description: string;
  status: 'pending' | 'confirmed' | 'rejected';
  resolved_action: string | null;
  resolved_by: string | null;
  created_by: string;
  created_at: string;
  user?: {
    full_name: string;
    email: string;
  };
  vehicle?: {
    make: string;
    model: string;
    year: number;
    vin: string;
  };
};

export default function AlertDetailPage() {
  const { user } = useAuth();
  const [alert, setAlert] = useState<Alert | null>(null);
  const [alertMedia, setAlertMedia] = useState<AlertMedia[]>([]);
  const [loading, setLoading] = useState(true);
  const [mediaLoading, setMediaLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<'pending' | 'confirmed' | 'rejected'>('pending');
  const [resolvedAction, setResolvedAction] = useState('');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { isAdmin, hasPermission, loading: permissionsLoading } = usePermissions();

  // Use the Next.js useParams hook to get the ID from the route
  const params = useParams();
  const alertId = typeof params.id === 'string' ? params.id : '';

  useEffect(() => {
    if (alertId) {
      fetchAlert();
      fetchAlertMedia();
    }
  }, [alertId]);

  const fetchAlert = async () => {
    try {
      const { data, error } = await supabase
        .from('alerts')
        .select(`
          *,
          alert_type:alert_type_id(name, description),
          user:created_by(full_name, email),
          vehicle:vehicle_id(make, model, year, vin)
        `)
        .eq('id', alertId)
        .single();

      if (error) {
        throw error;
      }

      setAlert(data);
    } catch (error) {
      console.error('Error fetching alert:', error);
      toast.error('Failed to load alert details');
    } finally {
      setLoading(false);
    }
  };

  const fetchAlertMedia = async () => {
    try {
      const { data, error } = await supabase
        .from('alert_media')
        .select('*')
        .eq('alert_id', alertId)
        .order('uploaded_at', { ascending: false });

      if (error) {
        throw error;
      }

      setAlertMedia(data || []);
    } catch (error) {
      console.error('Error fetching alert media:', error);
    } finally {
      setMediaLoading(false);
    }
  };

  const handleUpdateStatus = async () => {
    if (!alert || !user) return;

    setUpdateLoading(true);
    setError(null);

    try {
      const updateData: any = {
        status: newStatus,
      };

      if (newStatus !== 'pending') {
        updateData.resolved_by = user.id;
        updateData.resolved_action = resolvedAction;
      }

      const { error: updateError } = await supabase
        .from('alerts')
        .update(updateData)
        .eq('id', alert.id);

      if (updateError) {
        throw updateError;
      }

      toast.success('Alert status updated successfully');

      // Refresh alert data
      fetchAlert();
      setIsUpdateDialogOpen(false);
    } catch (error: any) {
      setError(error.message);
      toast.error('Failed to update alert status');
    } finally {
      setUpdateLoading(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'confirmed':
        return 'success';
      case 'rejected':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!alert) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">Alert Not Found</h2>
        <p className="text-neutral-600 mb-4">The requested alert could not be found.</p>
        <Link href="/dashboard/alerts">
          <Button>Back to Alerts</Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Alert Details</h1>
        <div className="flex gap-2">
          <Link href="/dashboard/alerts">
            <Button variant="outline">Back to Alerts</Button>
          </Link>
          {(isAdmin || hasPermission('alert:edit')) && alert.status === 'pending' && (
            <Button onClick={() => setIsUpdateDialogOpen(true)}>Update Status</Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Alert Information</CardTitle>
              <Badge variant={getStatusBadgeVariant(alert.status)}>
                {alert.status.charAt(0).toUpperCase() + alert.status.slice(1)}
              </Badge>
            </div>
            <CardDescription>
              {alert.alert_type?.name && (
                <span className="font-medium">
                  {alert.alert_type.name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </span>
              )}
              {alert.alert_type?.description && (
                <span className="ml-2 text-muted-foreground">
                  - {alert.alert_type.description}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-neutral-500 mb-1">Description</h3>
              <p className="text-neutral-800">{alert.description}</p>
            </div>

            {alert.license_plate && (
              <div>
                <h3 className="text-sm font-medium text-neutral-500 mb-1">License Plate</h3>
                <p className="text-neutral-800">{alert.license_plate}</p>
              </div>
            )}

            {alert.vehicle && (
              <div>
                <h3 className="text-sm font-medium text-neutral-500 mb-1">Vehicle</h3>
                <p className="text-neutral-800">
                  {alert.vehicle.make} {alert.vehicle.model} ({alert.vehicle.year})
                </p>
                <p className="text-sm text-neutral-500">VIN: {alert.vehicle.vin}</p>
                {alert.vehicle_id && (
                  <Link href={`/dashboard/vehicles/${alert.vehicle_id}`}>
                    <Button variant="outline" size="sm" className="mt-2">
                      View Vehicle
                    </Button>
                  </Link>
                )}
              </div>
            )}

            {alert.confidence_score !== null && (
              <div>
                <h3 className="text-sm font-medium text-neutral-500 mb-1">Confidence Score</h3>
                <p className="text-neutral-800">{(alert.confidence_score * 100).toFixed(1)}%</p>
              </div>
            )}

            <div>
              <h3 className="text-sm font-medium text-neutral-500 mb-1">Reported By</h3>
              <p className="text-neutral-800">
                {alert.user?.full_name || 'Unknown'}
                {alert.user?.email && <span className="text-sm text-neutral-500 ml-2">({alert.user.email})</span>}
              </p>
              <p className="text-sm text-neutral-500">
                Reported on {formatDate(alert.created_at)}
              </p>
            </div>

            {alert.status !== 'pending' && (
              <div>
                <h3 className="text-sm font-medium text-neutral-500 mb-1">Resolution</h3>
                <p className="text-neutral-800">
                  Status: <span className={`font-medium ${alert.status === 'confirmed' ? 'text-green-600' : 'text-red-600'}`}>
                    {alert.status.charAt(0).toUpperCase() + alert.status.slice(1)}
                  </span>
                </p>
                {alert.resolved_action && (
                  <p className="text-neutral-800 mt-1">
                    Action Taken: {alert.resolved_action}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Media</CardTitle>
              <CardDescription>
                Photos and documents related to this alert
              </CardDescription>
            </CardHeader>
            <CardContent>
              {mediaLoading ? (
                <div className="flex justify-center items-center h-24">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : alertMedia.length > 0 ? (
                <div className="grid grid-cols-2 gap-2">
                  {alertMedia.map((media) => (
                    <div key={media.id} className="relative">
                      {media.media_type === 'image' ? (
                        <a href={media.media_url} target="_blank" rel="noopener noreferrer">
                          <img
                            src={media.media_url}
                            alt={media.description || 'Alert media'}
                            className="w-full h-24 object-cover rounded-md"
                          />
                        </a>
                      ) : media.media_type === 'video' ? (
                        <a
                          href={media.media_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center h-24 bg-neutral-100 rounded-md"
                        >
                          <div className="text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-neutral-500 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                            <p className="text-xs text-neutral-500">Video</p>
                          </div>
                        </a>
                      ) : (
                        <a
                          href={media.media_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center h-24 bg-neutral-100 rounded-md"
                        >
                          <div className="text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-neutral-500 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <line x1="16" y1="13" x2="8" y2="13"></line>
                              <line x1="16" y1="17" x2="8" y2="17"></line>
                              <polyline points="10 9 9 9 8 9"></polyline>
                            </svg>
                            <p className="text-xs text-neutral-500">Document</p>
                          </div>
                        </a>
                      )}
                      <div className="mt-1 px-1">
                        <p className="text-xs truncate" title={media.description || ''}>
                          {media.description || 'Media file'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-neutral-500 text-center py-4">No media attached to this alert</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Alert Status</DialogTitle>
            <DialogDescription>
              Change the status of this alert and provide resolution details.
            </DialogDescription>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="status" className="text-right font-medium">
                Status
              </label>
              <div className="col-span-3">
                <Select
                  value={newStatus}
                  onValueChange={(value: any) => setNewStatus(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {newStatus !== 'pending' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="action" className="text-right font-medium">
                  Action Taken
                </label>
                <div className="col-span-3">
                  <Textarea
                    id="action"
                    placeholder="Describe the action taken to resolve this alert"
                    value={resolvedAction}
                    onChange={(e) => setResolvedAction(e.target.value)}
                    rows={3}
                  />
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleUpdateStatus}
              disabled={updateLoading || (newStatus !== 'pending' && !resolvedAction.trim())}
            >
              {updateLoading ? 'Updating...' : 'Update Status'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
