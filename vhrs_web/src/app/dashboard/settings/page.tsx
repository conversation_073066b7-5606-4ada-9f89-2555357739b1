'use client';

import { PermissionManager } from '@/components/settings/PermissionManager';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Settings2, Shield } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

// Define schemas for each settings section
const systemSettingsSchema = z.object({
  system_name: z.string().min(2, 'System name must be at least 2 characters'),
  contact_email: z.string().email('Please enter a valid email address'),
  support_phone: z.string().optional(),
  maintenance_mode: z.boolean().default(false),
  maintenance_message: z.string().optional(),
});

const alertSettingsSchema = z.object({
  default_alert_status: z.enum(['pending', 'in_progress', 'resolved', 'rejected']),
  require_approval: z.boolean().default(true),
  notify_admins: z.boolean().default(true),
  notify_agents: z.boolean().default(true),
  alert_categories: z.string(),
});

const securitySettingsSchema = z.object({
  max_login_attempts: z.number().min(1).max(10),
  session_timeout: z.number().min(15).max(1440),
  require_email_verification: z.boolean().default(true),
  allow_public_registration: z.boolean().default(true),
});

export default function SettingsPage() {
  const { isAdmin, loading: permissionsLoading } = usePermissions();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('system');
  const [loadingSettings, setLoadingSettings] = useState(true);

  // Create forms for each settings section
  const systemForm = useForm<z.infer<typeof systemSettingsSchema>>({
    resolver: zodResolver(systemSettingsSchema),
    defaultValues: {
      system_name: 'Vehicle History Record System',
      contact_email: '',
      support_phone: '',
      maintenance_mode: false,
      maintenance_message: '',
    },
  });

  const alertForm = useForm<z.infer<typeof alertSettingsSchema>>({
    resolver: zodResolver(alertSettingsSchema),
    defaultValues: {
      default_alert_status: 'pending',
      require_approval: true,
      notify_admins: true,
      notify_agents: true,
      alert_categories: 'accident,fraud,theft,damage,other',
    },
  });

  const securityForm = useForm<z.infer<typeof securitySettingsSchema>>({
    resolver: zodResolver(securitySettingsSchema),
    defaultValues: {
      max_login_attempts: 5,
      session_timeout: 60,
      require_email_verification: true,
      allow_public_registration: true,
    },
  });

  useEffect(() => {
    // Redirect non-admin users
    if (!permissionsLoading && !isAdmin) {
      toast.error('You do not have permission to access this page');
      router.push('/dashboard');
      return;
    }

    // Load settings from the database
    fetchSettings();
  }, [permissionsLoading, isAdmin, router]);

  const fetchSettings = async () => {
    setLoadingSettings(true);
    try {
      // Fetch active settings from the database
      const { data, error } = await supabase
        .from('system_settings')
        .select('*')
        .eq('is_active', true)
        .order('id', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('Error fetching settings:', error);
        toast.error('Failed to load settings');
        return;
      }

      if (data) {
        // Update form values with data from the database
        systemForm.reset({
          system_name: data.system_name || 'Vehicle History Record System',
          contact_email: data.contact_email || '',
          support_phone: data.support_phone || '',
          maintenance_mode: data.maintenance_mode || false,
          maintenance_message: data.maintenance_message || '',
        });

        alertForm.reset({
          default_alert_status: data.default_alert_status || 'pending',
          require_approval: data.require_approval !== false,
          notify_admins: data.notify_admins !== false,
          notify_agents: data.notify_agents !== false,
          alert_categories: data.alert_categories || 'accident,fraud,theft,damage,other',
        });

        securityForm.reset({
          max_login_attempts: data.max_login_attempts || 5,
          session_timeout: data.session_timeout || 60,
          require_email_verification: data.require_email_verification !== false,
          allow_public_registration: data.allow_public_registration !== false,
        });
      }
    } catch (error) {
      console.error('Error in fetchSettings:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoadingSettings(false);
    }
  };

  const onSystemSubmit = async (values: z.infer<typeof systemSettingsSchema>) => {
    try {
      // Get current user ID
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get current active settings
      const { data: currentSettings } = await supabase
        .from('system_settings')
        .select('*')
        .eq('is_active', true)
        .order('id', { ascending: false })
        .limit(1)
        .single();

      // Create a new settings object without the id field
      const { id, ...settingsWithoutId } = currentSettings || {};

      // Insert new settings record
      const { error } = await supabase
        .from('system_settings')
        .insert({
          // Copy all existing settings (except id)
          ...settingsWithoutId,
          // Override with new values
          system_name: values.system_name,
          contact_email: values.contact_email,
          support_phone: values.support_phone,
          maintenance_mode: values.maintenance_mode,
          maintenance_message: values.maintenance_message,
          // Set metadata
          created_by: user.id,
          is_active: true
        });

      if (error) throw error;
      toast.success('System settings updated successfully');
    } catch (error) {
      console.error('Error updating system settings:', error);
      toast.error('Failed to update system settings');
    }
  };

  const onAlertSubmit = async (values: z.infer<typeof alertSettingsSchema>) => {
    try {
      // Get current user ID
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get current active settings
      const { data: currentSettings } = await supabase
        .from('system_settings')
        .select('*')
        .eq('is_active', true)
        .order('id', { ascending: false })
        .limit(1)
        .single();

      // Create a new settings object without the id field
      const { id, ...settingsWithoutId } = currentSettings || {};

      // Insert new settings record
      const { error } = await supabase
        .from('system_settings')
        .insert({
          // Copy all existing settings (except id)
          ...settingsWithoutId,
          // Override with new values
          default_alert_status: values.default_alert_status,
          require_approval: values.require_approval,
          notify_admins: values.notify_admins,
          notify_agents: values.notify_agents,
          alert_categories: values.alert_categories,
          // Set metadata
          created_by: user.id,
          is_active: true
        });

      if (error) throw error;
      toast.success('Alert settings updated successfully');
    } catch (error) {
      console.error('Error updating alert settings:', error);
      toast.error('Failed to update alert settings');
    }
  };

  const onSecuritySubmit = async (values: z.infer<typeof securitySettingsSchema>) => {
    try {
      // Get current user ID
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get current active settings
      const { data: currentSettings } = await supabase
        .from('system_settings')
        .select('*')
        .eq('is_active', true)
        .order('id', { ascending: false })
        .limit(1)
        .single();

      // Create a new settings object without the id field
      const { id, ...settingsWithoutId } = currentSettings || {};

      // Insert new settings record
      const { error } = await supabase
        .from('system_settings')
        .insert({
          // Copy all existing settings (except id)
          ...settingsWithoutId,
          // Override with new values
          max_login_attempts: values.max_login_attempts,
          session_timeout: values.session_timeout,
          require_email_verification: values.require_email_verification,
          allow_public_registration: values.allow_public_registration,
          // Set metadata
          created_by: user.id,
          is_active: true
        });

      if (error) throw error;
      toast.success('Security settings updated successfully');
    } catch (error) {
      console.error('Error updating security settings:', error);
      toast.error('Failed to update security settings');
    }
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
          <p className="text-neutral-500">
            Configure system-wide settings and preferences
          </p>
        </div>
        <Link href="/dashboard/settings/history">
          <Button variant="outline" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span>View History</span>
          </Button>
        </Link>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings2 className="h-4 w-4" />
            <span>General</span>
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            <span>Alerts</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            <span>Permissions</span>
          </TabsTrigger>
        </TabsList>

        {/* System Settings Tab */}
        <TabsContent value="system" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Configure basic system settings and contact information
              </CardDescription>
            </CardHeader>
            <Form {...systemForm}>
              <form onSubmit={systemForm.handleSubmit(onSystemSubmit)}>
                <CardContent className="space-y-4">
                  <FormField
                    control={systemForm.control}
                    name="system_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>System Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormDescription>
                          The name displayed throughout the system
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={systemForm.control}
                    name="contact_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Email</FormLabel>
                        <FormControl>
                          <Input {...field} type="email" />
                        </FormControl>
                        <FormDescription>
                          Primary contact email for system notifications
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={systemForm.control}
                    name="support_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Support Phone</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormDescription>
                          Optional support phone number
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={systemForm.control}
                    name="maintenance_mode"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Maintenance Mode
                          </FormLabel>
                          <FormDescription>
                            Enable maintenance mode to restrict access to the system
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={systemForm.control}
                    name="maintenance_message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maintenance Message</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="The system is currently undergoing maintenance. Please check back later."
                            className="resize-none"
                            rows={3}
                          />
                        </FormControl>
                        <FormDescription>
                          Message to display during maintenance mode
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={loadingSettings}>
                    Save General Settings
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </TabsContent>

        {/* Alert Settings Tab */}
        <TabsContent value="alerts" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Alert Settings</CardTitle>
              <CardDescription>
                Configure how alerts are processed and managed
              </CardDescription>
            </CardHeader>
            <Form {...alertForm}>
              <form onSubmit={alertForm.handleSubmit(onAlertSubmit)}>
                <CardContent className="space-y-4">
                  <FormField
                    control={alertForm.control}
                    name="default_alert_status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Alert Status</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select default status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="in_progress">In Progress</SelectItem>
                            <SelectItem value="resolved">Resolved</SelectItem>
                            <SelectItem value="rejected">Rejected</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Status assigned to new alerts
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={alertForm.control}
                    name="require_approval"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Require Approval
                          </FormLabel>
                          <FormDescription>
                            Require admin or agent approval for alerts
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={alertForm.control}
                    name="notify_admins"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Notify Admins
                          </FormLabel>
                          <FormDescription>
                            Send notifications to admins for new alerts
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={alertForm.control}
                    name="notify_agents"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Notify Agents
                          </FormLabel>
                          <FormDescription>
                            Send notifications to agents with alert management permissions for new alerts
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={alertForm.control}
                    name="alert_categories"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Alert Categories</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="accident,fraud,theft,damage,other"
                            className="resize-none"
                            rows={3}
                          />
                        </FormControl>
                        <FormDescription>
                          Comma-separated list of alert categories
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={loadingSettings}>
                    Save Alert Settings
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </TabsContent>

        {/* Security Settings Tab */}
        <TabsContent value="security" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Configure security and authentication settings
              </CardDescription>
            </CardHeader>
            <Form {...securityForm}>
              <form onSubmit={securityForm.handleSubmit(onSecuritySubmit)}>
                <CardContent className="space-y-4">
                  <FormField
                    control={securityForm.control}
                    name="max_login_attempts"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Max Login Attempts</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            min={1}
                            max={10}
                            onChange={(e) => field.onChange(parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum number of failed login attempts before lockout
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={securityForm.control}
                    name="session_timeout"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Session Timeout (minutes)</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            min={15}
                            max={1440}
                            onChange={(e) => field.onChange(parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Time in minutes before an inactive session expires
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={securityForm.control}
                    name="require_email_verification"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Require Email Verification
                          </FormLabel>
                          <FormDescription>
                            Require users to verify their email before accessing the system
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={securityForm.control}
                    name="allow_public_registration"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Allow Public Registration
                          </FormLabel>
                          <FormDescription>
                            Allow users to register accounts without admin approval
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={loadingSettings}>
                    Save Security Settings
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="mt-6">
          <PermissionManager />
        </TabsContent>
      </Tabs>
    </div>
  );
}
