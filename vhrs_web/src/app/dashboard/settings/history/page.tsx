'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

type SettingsHistory = {
  id: number;
  system_name: string;
  created_by: string;
  created_at: string;
  is_active: boolean;
  user_full_name?: string;
  user_email?: string;
};

export default function SettingsHistoryPage() {
  const { isAdmin, loading: permissionsLoading } = usePermissions();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [history, setHistory] = useState<SettingsHistory[]>([]);

  useEffect(() => {
    // Redirect non-admin users
    if (!permissionsLoading && !isAdmin) {
      toast.error('You do not have permission to access this page');
      router.push('/dashboard');
      return;
    }

    fetchSettingsHistory();
  }, [permissionsLoading, isAdmin, router]);

  const fetchSettingsHistory = async () => {
    setLoading(true);
    try {
      // Fetch settings history with user information
      const { data, error } = await supabase
        .from('system_settings')
        .select(`
          id,
          system_name,
          created_by,
          created_at,
          is_active,
          profiles:created_by (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Format the data
      const formattedHistory = data?.map((item) => ({
        id: item.id,
        system_name: item.system_name,
        created_by: item.created_by,
        created_at: item.created_at,
        is_active: item.is_active,
        user_full_name: item.profiles?.full_name,
        user_email: item.profiles?.email,
      })) || [];

      setHistory(formattedHistory);
    } catch (error) {
      console.error('Error fetching settings history:', error);
      toast.error('Failed to load settings history');
    } finally {
      setLoading(false);
    }
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link href="/dashboard/settings">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Settings History</h1>
        </div>
        <Button onClick={fetchSettingsHistory} variant="outline" className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>System Settings Change History</CardTitle>
          <CardDescription>
            View a history of all changes made to system settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-500"></div>
            </div>
          ) : history.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>System Name</TableHead>
                  <TableHead>Changed By</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.id}</TableCell>
                    <TableCell>{item.system_name}</TableCell>
                    <TableCell>
                      {item.user_full_name || item.user_email || 'Unknown User'}
                    </TableCell>
                    <TableCell>{formatDate(item.created_at)}</TableCell>
                    <TableCell>
                      {item.is_active ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800">
                          Previous
                        </span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-neutral-500">
              No settings history found
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
