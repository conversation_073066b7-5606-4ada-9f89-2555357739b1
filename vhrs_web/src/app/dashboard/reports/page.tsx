'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import {
    ArcElement,
    BarElement,
    CategoryScale,
    Chart as ChartJS,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    Tooltip,
} from 'chart.js';
import { BarChart, Calendar, Download, FileText, TrendingUp, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Pie } from 'react-chartjs-2';
import { toast } from 'sonner';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

export default function ReportsPage() {
  const { isAdmin, hasPermission, loading: permissionsLoading } = usePermissions();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('30days');
  const [loadingData, setLoadingData] = useState(true);

  // Stats data
  const [vehicleStats, setVehicleStats] = useState<any>({
    totalVehicles: 0,
    vehiclesByType: {},
    vehiclesByYear: {},
    vehiclesByMake: {},
  });

  const [alertStats, setAlertStats] = useState<any>({
    totalAlerts: 0,
    alertsByStatus: {},
    alertsByType: {},
    alertsOverTime: {},
  });

  const [userStats, setUserStats] = useState<any>({
    totalUsers: 0,
    usersByRole: {},
    newUsersOverTime: {},
    activeUsers: 0,
  });

  const [historyStats, setHistoryStats] = useState<any>({
    totalRequests: 0,
    requestsByStatus: {},
    requestsOverTime: {},
  });

  useEffect(() => {
    // Redirect users without proper permissions
    if (!permissionsLoading && !isAdmin && !hasPermission('vehicle:view')) {
      toast.error('You do not have permission to access this page');
      router.push('/dashboard');
      return;
    }

    // Load report data
    fetchReportData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [permissionsLoading, isAdmin, hasPermission, router, timeRange, activeTab]);

  const fetchReportData = async () => {
    setLoadingData(true);
    try {
      // Calculate date range based on selected time range
      const endDate = new Date();
      const startDate = new Date();

      switch (timeRange) {
        case '7days':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30days':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90days':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      const startDateStr = startDate.toISOString();
      const endDateStr = endDate.toISOString();

      // Fetch data based on active tab
      switch (activeTab) {
        case 'overview':
          await Promise.all([
            fetchVehicleStats(startDateStr, endDateStr),
            fetchAlertStats(startDateStr, endDateStr),
            fetchUserStats(startDateStr, endDateStr),
            fetchHistoryStats(startDateStr, endDateStr),
          ]);
          break;
        case 'vehicles':
          await fetchVehicleStats(startDateStr, endDateStr);
          break;
        case 'alerts':
          await fetchAlertStats(startDateStr, endDateStr);
          break;
        case 'users':
          await fetchUserStats(startDateStr, endDateStr);
          break;
        case 'history':
          await fetchHistoryStats(startDateStr, endDateStr);
          break;
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('Failed to load report data');
    } finally {
      setLoadingData(false);
    }
  };

  const fetchVehicleStats = async (startDate: string, endDate: string) => {
    try {
      // Get total vehicles
      const { count: totalVehicles } = await supabase
        .from('vehicles')
        .select('*', { count: 'exact', head: true });

      // Get vehicles by type
      const { data: vehiclesByTypeData } = await supabase
        .from('vehicles')
        .select('vehicle_type')
        .not('vehicle_type', 'is', null);

      const vehiclesByType = vehiclesByTypeData?.reduce((acc: any, vehicle) => {
        const type = vehicle.vehicle_type || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});

      // Get vehicles by year
      const { data: vehiclesByYearData } = await supabase
        .from('vehicles')
        .select('year')
        .not('year', 'is', null);

      const vehiclesByYear = vehiclesByYearData?.reduce((acc: any, vehicle) => {
        const year = vehicle.year || 'unknown';
        acc[year] = (acc[year] || 0) + 1;
        return acc;
      }, {});

      // Get vehicles by make
      const { data: vehiclesByMakeData } = await supabase
        .from('vehicles')
        .select('make')
        .not('make', 'is', null);

      const vehiclesByMake = vehiclesByMakeData?.reduce((acc: any, vehicle) => {
        const make = vehicle.make || 'unknown';
        acc[make] = (acc[make] || 0) + 1;
        return acc;
      }, {});

      // Get new vehicles over time
      const { data: newVehiclesData } = await supabase
        .from('vehicles')
        .select('created_at')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      const newVehiclesOverTime = newVehiclesData?.reduce((acc: any, vehicle) => {
        const date = new Date(vehicle.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});

      setVehicleStats({
        totalVehicles: totalVehicles || 0,
        vehiclesByType,
        vehiclesByYear,
        vehiclesByMake,
        newVehiclesOverTime,
      });
    } catch (error) {
      console.error('Error fetching vehicle stats:', error);
    }
  };

  const fetchAlertStats = async (startDate: string, endDate: string) => {
    try {
      // Get total alerts
      const { count: totalAlerts } = await supabase
        .from('alerts')
        .select('*', { count: 'exact', head: true });

      // Get alerts by status
      const { data: alertsByStatusData } = await supabase
        .from('alerts')
        .select('status');

      const alertsByStatus = alertsByStatusData?.reduce((acc: any, alert) => {
        const status = alert.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});

      // Get alerts by type
      const { data: alertsByTypeData } = await supabase
        .from('alerts')
        .select('alert_type_id, alert_type:alert_type_id(name)');

      const alertsByType = alertsByTypeData?.reduce((acc: any, alert) => {
        const type = alert.alert_type?.name || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});

      // Get alerts over time
      const { data: alertsOverTimeData } = await supabase
        .from('alerts')
        .select('created_at')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      const alertsOverTime = alertsOverTimeData?.reduce((acc: any, alert) => {
        const date = new Date(alert.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});

      setAlertStats({
        totalAlerts: totalAlerts || 0,
        alertsByStatus,
        alertsByType,
        alertsOverTime,
      });
    } catch (error) {
      console.error('Error fetching alert stats:', error);
    }
  };

  const fetchUserStats = async (startDate: string, endDate: string) => {
    try {
      // Get total users
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Get users by role
      const { data: usersByRoleData } = await supabase
        .from('user_roles')
        .select('roles(name)');

      const usersByRole = usersByRoleData?.reduce((acc: any, userRole) => {
        const role = userRole.roles?.name || 'unknown';
        acc[role] = (acc[role] || 0) + 1;
        return acc;
      }, {});

      // Get new users over time
      const { data: newUsersData } = await supabase
        .from('profiles')
        .select('created_at')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      const newUsersOverTime = newUsersData?.reduce((acc: any, user) => {
        const date = new Date(user.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});

      // Get active users (users with activity in the last 30 days)
      const { count: activeUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('last_active_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      setUserStats({
        totalUsers: totalUsers || 0,
        usersByRole,
        newUsersOverTime,
        activeUsers: activeUsers || 0,
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
    }
  };

  const fetchHistoryStats = async (startDate: string, endDate: string) => {
    try {
      // Get total history requests
      const { count: totalRequests } = await supabase
        .from('history_report_requests')
        .select('*', { count: 'exact', head: true });

      // Get requests by status
      const { data: requestsByStatusData } = await supabase
        .from('history_report_requests')
        .select('status');

      const requestsByStatus = requestsByStatusData?.reduce((acc: any, request) => {
        const status = request.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});

      // Get requests over time
      const { data: requestsOverTimeData } = await supabase
        .from('history_report_requests')
        .select('created_at')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      const requestsOverTime = requestsOverTimeData?.reduce((acc: any, request) => {
        const date = new Date(request.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});

      setHistoryStats({
        totalRequests: totalRequests || 0,
        requestsByStatus,
        requestsOverTime,
      });
    } catch (error) {
      console.error('Error fetching history stats:', error);
    }
  };

  const handleExportData = () => {
    let dataToExport: any = {};

    switch (activeTab) {
      case 'overview':
        dataToExport = {
          vehicleStats,
          alertStats,
          userStats,
          historyStats,
        };
        break;
      case 'vehicles':
        dataToExport = vehicleStats;
        break;
      case 'alerts':
        dataToExport = alertStats;
        break;
      case 'users':
        dataToExport = userStats;
        break;
      case 'history':
        dataToExport = historyStats;
        break;
    }

    // Create a JSON blob and download it
    const dataStr = JSON.stringify(dataToExport, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `vhrs_report_${activeTab}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Report data exported successfully');
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  if (!isAdmin && !hasPermission('vehicle:view')) {
    return null; // Router will redirect
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Reports</h1>
          <p className="text-neutral-500">
            View and analyze system statistics and trends
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="1year">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleExportData} variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            <span>Export Data</span>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="vehicles" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Vehicles</span>
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            <span>Alerts</span>
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Users</span>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>History Records</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Vehicle Statistics</CardTitle>
                <CardDescription>
                  Overview of vehicle data in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingData ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-500"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-sm text-neutral-500">Total Vehicles</p>
                      <p className="text-3xl font-bold">{vehicleStats.totalVehicles}</p>
                    </div>
                    <div className="h-64">
                      {vehicleStats.vehiclesByType && Object.keys(vehicleStats.vehiclesByType).length > 0 && (
                        <Pie
                          data={{
                            labels: Object.keys(vehicleStats.vehiclesByType),
                            datasets: [
                              {
                                data: Object.values(vehicleStats.vehiclesByType),
                                backgroundColor: [
                                  '#4f46e5',
                                  '#0ea5e9',
                                  '#10b981',
                                  '#f59e0b',
                                  '#ef4444',
                                  '#8b5cf6',
                                ],
                              },
                            ],
                          }}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                              legend: {
                                position: 'bottom',
                              },
                              title: {
                                display: true,
                                text: 'Vehicles by Type',
                              },
                            },
                          }}
                        />
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Alert Statistics</CardTitle>
                <CardDescription>
                  Overview of alert data in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingData ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-500"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-sm text-neutral-500">Total Alerts</p>
                      <p className="text-3xl font-bold">{alertStats.totalAlerts}</p>
                    </div>
                    <div className="h-64">
                      {alertStats.alertsByStatus && Object.keys(alertStats.alertsByStatus).length > 0 && (
                        <Pie
                          data={{
                            labels: Object.keys(alertStats.alertsByStatus),
                            datasets: [
                              {
                                data: Object.values(alertStats.alertsByStatus),
                                backgroundColor: [
                                  '#4f46e5',
                                  '#0ea5e9',
                                  '#10b981',
                                  '#f59e0b',
                                  '#ef4444',
                                ],
                              },
                            ],
                          }}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                              legend: {
                                position: 'bottom',
                              },
                              title: {
                                display: true,
                                text: 'Alerts by Status',
                              },
                            },
                          }}
                        />
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Statistics</CardTitle>
                <CardDescription>
                  Overview of user data in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingData ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-500"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <p className="text-sm text-neutral-500">Total Users</p>
                        <p className="text-3xl font-bold">{userStats.totalUsers}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-neutral-500">Active Users</p>
                        <p className="text-3xl font-bold">{userStats.activeUsers}</p>
                      </div>
                    </div>
                    <div className="h-64">
                      {userStats.usersByRole && Object.keys(userStats.usersByRole).length > 0 && (
                        <Pie
                          data={{
                            labels: Object.keys(userStats.usersByRole),
                            datasets: [
                              {
                                data: Object.values(userStats.usersByRole),
                                backgroundColor: [
                                  '#4f46e5',
                                  '#0ea5e9',
                                  '#10b981',
                                ],
                              },
                            ],
                          }}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                              legend: {
                                position: 'bottom',
                              },
                              title: {
                                display: true,
                                text: 'Users by Role',
                              },
                            },
                          }}
                        />
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>History Record Statistics</CardTitle>
                <CardDescription>
                  Overview of history record requests
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingData ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-500"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-sm text-neutral-500">Total Requests</p>
                      <p className="text-3xl font-bold">{historyStats.totalRequests}</p>
                    </div>
                    <div className="h-64">
                      {historyStats.requestsByStatus && Object.keys(historyStats.requestsByStatus).length > 0 && (
                        <Pie
                          data={{
                            labels: Object.keys(historyStats.requestsByStatus),
                            datasets: [
                              {
                                data: Object.values(historyStats.requestsByStatus),
                                backgroundColor: [
                                  '#4f46e5',
                                  '#0ea5e9',
                                  '#10b981',
                                  '#f59e0b',
                                  '#ef4444',
                                ],
                              },
                            ],
                          }}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                              legend: {
                                position: 'bottom',
                              },
                              title: {
                                display: true,
                                text: 'Requests by Status',
                              },
                            },
                          }}
                        />
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Other tabs would be implemented similarly */}
        <TabsContent value="vehicles" className="mt-6">
          {/* Vehicle-specific charts and statistics */}
        </TabsContent>

        <TabsContent value="alerts" className="mt-6">
          {/* Alert-specific charts and statistics */}
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          {/* User-specific charts and statistics */}
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          {/* History record-specific charts and statistics */}
        </TabsContent>
      </Tabs>
    </div>
  );
}
