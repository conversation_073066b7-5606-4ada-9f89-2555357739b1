'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Vehicle {
  id: string;
  vin: string;
  make: string;
  model: string;
  year: number;
  vehicle_type: string;
  created_at: string;
  vehicle_license_plates: {
    license_plate: string;
  }[] | null;
}

interface HistoryRequest {
  id: string;
  user_id: string;
  vehicle_id: string;
  license_plate: string | null;
  vin: string | null;
  request_method: string;
  status: string;
  document_url: string | null;
  viewed_in_app: boolean;
  created_at: string;
  completed_at: string | null;
  vehicles: {
    make: string;
    model: string;
    year: number;
  } | null;
}

export default function RequestVehicleHistory() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const vehicleId = searchParams.get('vehicle_id');
  const searchType = searchParams.get('search_type');
  const searchTerm = searchParams.get('search_term');

  // Vehicle search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchMethod, setSearchMethod] = useState('plate');
  const [searchResults, setSearchResults] = useState<Vehicle[]>([]);
  const [searching, setSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Dialog state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);

  // Vehicle request state
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // My requests state
  const [myRequests, setMyRequests] = useState<HistoryRequest[]>([]);
  const [requestsLoading, setRequestsLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [requestSearchTerm, setRequestSearchTerm] = useState('');

  // No redirect needed as we now have search functionality on this page

  // Fetch vehicle details if vehicle_id is provided
  useEffect(() => {
    async function fetchVehicle() {
      if (!vehicleId) return;

      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('vehicles')
          .select('*, vehicle_license_plates(license_plate)')
          .eq('id', vehicleId)
          .single();

        if (error) throw error;
        setVehicle(data);
      } catch (error) {
        console.error('Error fetching vehicle:', error);
        toast.error('Failed to load vehicle details');
      } finally {
        setLoading(false);
      }
    }

    fetchVehicle();
  }, [vehicleId]);

  // Fetch user's history requests
  useEffect(() => {
    if (!user) return;
    fetchMyRequests();
  }, [user, statusFilter]);

  const fetchMyRequests = async () => {
    if (!user) return;

    setRequestsLoading(true);
    try {
      let query = supabase
        .from('history_report_requests')
        .select('*, vehicles(make, model, year)')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      // Apply status filter if not 'all'
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      const { data, error } = await query;

      if (error) throw error;
      setMyRequests(data || []);
    } catch (error) {
      console.error('Error fetching history requests:', error);
      toast.error('Failed to load your history requests');
    } finally {
      setRequestsLoading(false);
    }
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  const handleRequestSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRequestSearchTerm(e.target.value);
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setSearching(true);
    setHasSearched(true);

    try {
      let query = supabase
        .from('vehicles')
        .select('*, vehicle_license_plates(license_plate)');

      if (searchMethod === 'plate') {
        // Search by plate number
        const { data: plateData } = await supabase
          .from('vehicle_license_plates')
          .select('vehicle_id')
          .ilike('license_plate', `%${searchQuery}%`);

        if (plateData && plateData.length > 0) {
          const vehicleIds = plateData.map(plate => plate.vehicle_id);
          query = query.in('id', vehicleIds);
        } else {
          setSearchResults([]);
          setSearching(false);
          return;
        }
      } else if (searchMethod === 'vin') {
        // Search by VIN
        query = query.ilike('vin', `%${searchQuery}%`);
      }

      const { data, error } = await query.limit(10);

      if (error) {
        throw error;
      }

      setSearchResults(data || []);
    } catch (error) {
      console.error('Error searching vehicles:', error);
      toast.error('An error occurred while searching for vehicles');
    } finally {
      setSearching(false);
    }
  };

  const getPlateNumber = (vehicle: Vehicle): string => {
    if (vehicle.vehicle_license_plates && vehicle.vehicle_license_plates.length > 0) {
      return vehicle.vehicle_license_plates[0].license_plate;
    }
    return 'N/A';
  };

  // Filter requests by search term
  const filteredRequests = myRequests.filter((request) => {
    if (!requestSearchTerm) return true;

    const searchLower = requestSearchTerm.toLowerCase();
    return (
      (request.vehicles?.make && request.vehicles.make.toLowerCase().includes(searchLower)) ||
      (request.vehicles?.model && request.vehicles.model.toLowerCase().includes(searchLower)) ||
      (request.license_plate && request.license_plate.toLowerCase().includes(searchLower)) ||
      (request.vin && request.vin.toLowerCase().includes(searchLower))
    );
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>;
      case 'processing':
        return <Badge variant="secondary">Processing</Badge>;
      case 'generated':
        return <Badge variant="default">Generated</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error('You must be logged in to request a vehicle history report');
      return;
    }

    if (!vehicleId || !vehicle) {
      toast.error('A valid vehicle must be selected to request a history report');
      router.push('/dashboard/vehicles/request');
      return;
    }

    // Validate search type if provided
    if (searchType && !['plate', 'vin', 'chassis'].includes(searchType)) {
      toast.error('Invalid search type provided');
      return;
    }

    setSubmitting(true);

    try {
      // Determine request method and additional fields
      let requestMethod = 'manual';
      const requestData: any = {
        user_id: user.id,
        vehicle_id: vehicleId,
        status: 'pending',
      };

      // If search type and term are available, use them
      if (searchType && searchTerm) {
        if (searchType === 'plate') {
          requestMethod = 'license_plate';
          requestData.license_plate = searchTerm;
        } else if (searchType === 'vin') {
          requestMethod = 'vin';
          requestData.vin = searchTerm;
        }
      }

      requestData.request_method = requestMethod;

      const { error: insertError } = await supabase
        .from('history_report_requests')
        .insert([requestData]);

      if (insertError) {
        throw insertError;
      }

      toast.success('Vehicle history record request submitted successfully');
      router.push('/dashboard');
    } catch (error) {
      console.error('Error submitting request:', error);
      toast.error('Failed to submit vehicle history record request');
    } finally {
      setSubmitting(false);
    }
  };

  // If no vehicle is selected, show the search interface
  if (!vehicleId) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Request Vehicle History</h1>
          <p className="text-neutral-500">
            Search for a vehicle to request its history record
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Search for a Vehicle</CardTitle>
            <CardDescription>
              Find a vehicle by license plate or VIN to request its history record
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  id="searchQuery"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Enter license plate or VIN"
                  required
                />
              </div>
              <div className="w-full sm:w-48">
                <select
                  id="searchMethod"
                  value={searchMethod}
                  onChange={(e) => setSearchMethod(e.target.value)}
                  className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="plate">License Plate</option>
                  <option value="vin">VIN</option>
                </select>
              </div>
              <div className="flex gap-2">
                {hasSearched && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setSearchQuery('');
                      setHasSearched(false);
                      setSearchResults([]);
                    }}
                    disabled={searching}
                  >
                    Clear
                  </Button>
                )}
                <Button type="submit" disabled={searching}>
                  {searching ? 'Searching...' : 'Search'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {hasSearched && (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Search Results</CardTitle>
                  <CardDescription>
                    {searchResults.length > 0
                      ? `Found ${searchResults.length} vehicle(s) matching your search criteria`
                      : 'No vehicles found matching your search criteria'}
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchQuery('');
                    setHasSearched(false);
                    setSearchResults([]);
                  }}
                >
                  Cancel Search
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {searching ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Make & Model</TableHead>
                        <TableHead>Year</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>VIN</TableHead>
                        <TableHead>License Plate</TableHead>
                        <TableHead>Added</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {searchResults.map((vehicle) => (
                        <TableRow key={vehicle.id}>
                          <TableCell className="font-medium">
                            {vehicle.make} {vehicle.model}
                          </TableCell>
                          <TableCell>{vehicle.year}</TableCell>
                          <TableCell className="capitalize">{vehicle.vehicle_type}</TableCell>
                          <TableCell className="font-mono text-xs">{vehicle.vin}</TableCell>
                          <TableCell className="font-mono">{getPlateNumber(vehicle)}</TableCell>
                          <TableCell>{formatDate(vehicle.created_at)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedVehicle(vehicle);
                                  setIsDialogOpen(true);
                                }}
                              >
                                Details
                              </Button>
                              <Link href={`/dashboard/vehicles/request?vehicle_id=${vehicle.id}&search_type=${searchMethod}&search_term=${searchQuery}`}>
                                <Button size="sm">Request History</Button>
                              </Link>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-neutral-500">No vehicles found matching your search criteria.</p>
                  <p className="mt-2">
                    Try a different search term or search method.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Vehicle Details Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Vehicle Details</DialogTitle>
              <DialogDescription>
                Basic information about the vehicle
              </DialogDescription>
            </DialogHeader>

            {selectedVehicle && (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mt-4">
                <div>
                  <Label className="font-medium">Make & Model</Label>
                  <p className="mt-1">{selectedVehicle.make} {selectedVehicle.model}</p>
                </div>
                <div>
                  <Label className="font-medium">Year</Label>
                  <p className="mt-1">{selectedVehicle.year}</p>
                </div>
                <div>
                  <Label className="font-medium">Vehicle Type</Label>
                  <p className="mt-1 capitalize">{selectedVehicle.vehicle_type}</p>
                </div>
                <div>
                  <Label className="font-medium">VIN</Label>
                  <p className="mt-1 font-mono text-xs">{selectedVehicle.vin}</p>
                </div>
                <div>
                  <Label className="font-medium">License Plate</Label>
                  <p className="mt-1 font-mono">{getPlateNumber(selectedVehicle)}</p>
                </div>
                <div>
                  <Label className="font-medium">Added</Label>
                  <p className="mt-1">{formatDate(selectedVehicle.created_at)}</p>
                </div>
              </div>
            )}

            <div className="flex justify-end mt-6">
              <Button
                onClick={() => {
                  setIsDialogOpen(false);
                }}
              >
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* My History Requests Section */}
        {user && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>My Report Requests</CardTitle>
              <CardDescription>
                Your previous vehicle history report requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <Input
                    placeholder="Search by vehicle, VIN, or plate..."
                    value={requestSearchTerm}
                    onChange={handleRequestSearch}
                    className="w-full"
                  />
                </div>

                <div className="w-full md:w-48">
                  <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="generated">Generated</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {requestsLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : filteredRequests.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Vehicle</TableHead>
                        <TableHead>Identifier</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRequests.map((request) => (
                        <TableRow key={request.id}>
                          <TableCell className="font-medium">
                            {request.vehicles ? (
                              `${request.vehicles.make} ${request.vehicles.model} (${request.vehicles.year})`
                            ) : (
                              'Unknown Vehicle'
                            )}
                          </TableCell>
                          <TableCell>
                            {request.license_plate ? (
                              <span className="font-mono">LP: {request.license_plate}</span>
                            ) : request.vin ? (
                              <span className="font-mono text-xs">VIN: {request.vin}</span>
                            ) : (
                              'N/A'
                            )}
                          </TableCell>
                          <TableCell>{formatDate(request.created_at)}</TableCell>
                          <TableCell>{getStatusBadge(request.status)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              {request.status === 'generated' && (
                                <Link href={`/dashboard/vehicles/${request.vehicle_id}/history`}>
                                  <Button size="sm">View Record</Button>
                                </Link>
                              )}
                              <Link href={`/dashboard/vehicles/${request.vehicle_id}`}>
                                <Button variant="outline" size="sm">View Vehicle</Button>
                              </Link>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-neutral-500">You have not requested any vehicle history reports yet.</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Request Vehicle Report</h1>
        <p className="text-neutral-500">
          Request a comprehensive history report for a vehicle
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Vehicle Report Request</CardTitle>
          <CardDescription>
            Confirm your request for this vehicle's history report
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-6">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              {vehicle ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <Label>Make & Model</Label>
                      <div className="mt-1 p-2 border rounded-md bg-neutral-50">
                        {vehicle.make} {vehicle.model}
                      </div>
                    </div>
                    <div>
                      <Label>Year</Label>
                      <div className="mt-1 p-2 border rounded-md bg-neutral-50">
                        {vehicle.year}
                      </div>
                    </div>
                    <div>
                      <Label>VIN</Label>
                      <div className="mt-1 p-2 border rounded-md bg-neutral-50">
                        {vehicle.vin || 'Not available'}
                      </div>
                    </div>
                    <div>
                      <Label>License Plate</Label>
                      <div className="mt-1 p-2 border rounded-md bg-neutral-50">
                        {vehicle.vehicle_license_plates && vehicle.vehicle_license_plates.length > 0
                          ? vehicle.vehicle_license_plates[0]?.license_plate
                          : 'Not available'}
                      </div>
                    </div>
                    {searchType && searchTerm && (
                      <div className="sm:col-span-2">
                        <Label>Search Method</Label>
                        <div className="mt-1 p-2 border rounded-md bg-neutral-50">
                          Found by {searchType === 'plate' ? 'license plate' : searchType === 'vin' ? 'VIN' : 'chassis number'}: <span className="font-medium">{searchTerm}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-neutral-500">
                    By submitting this request, you will receive a comprehensive history report for this vehicle.
                  </p>
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-neutral-500">
                    Vehicle details could not be loaded. Please try again or select a different vehicle.
                  </p>
                  <div className="mt-4">
                    <Link href="/dashboard/vehicles/request">
                      <Button variant="outline">Return to Vehicle Search</Button>
                    </Link>
                  </div>
                </div>
              )}

              {vehicle && (
                <div className="flex justify-end space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      // Return to the search interface on this page
                      router.push('/dashboard/vehicles/request');
                    }}
                    disabled={submitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={submitting}>
                    {submitting ? 'Submitting...' : 'Submit Request'}
                  </Button>
                </div>
              )}
            </form>
          )}
        </CardContent>
      </Card>

      {/* My History Requests Section */}
      {user && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>My Report Requests</CardTitle>
            <CardDescription>
              Your previous vehicle history report requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <Input
                  placeholder="Search by vehicle, VIN, or plate..."
                  value={requestSearchTerm}
                  onChange={handleRequestSearch}
                  className="w-full"
                />
              </div>

              <div className="w-full md:w-48">
                <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="generated">Generated</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {requestsLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredRequests.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Vehicle</TableHead>
                      <TableHead>Identifier</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">
                          {request.vehicles ? (
                            `${request.vehicles.make} ${request.vehicles.model} (${request.vehicles.year})`
                          ) : (
                            'Unknown Vehicle'
                          )}
                        </TableCell>
                        <TableCell>
                          {request.license_plate ? (
                            <span className="font-mono">LP: {request.license_plate}</span>
                          ) : request.vin ? (
                            <span className="font-mono text-xs">VIN: {request.vin}</span>
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                        <TableCell>{formatDate(request.created_at)}</TableCell>
                        <TableCell>{getStatusBadge(request.status)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            {request.status === 'generated' && (
                              <Link href={`/dashboard/vehicles/${request.vehicle_id}/history`}>
                                <Button size="sm">View Report</Button>
                              </Link>
                            )}
                            <Link href={`/dashboard/vehicles/${request.vehicle_id}`}>
                              <Button variant="outline" size="sm">View Vehicle</Button>
                            </Link>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-neutral-500">You have not requested any vehicle history reports yet.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
