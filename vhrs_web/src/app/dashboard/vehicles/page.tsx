'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Vehicle {
  id: string;
  vin: string;
  make: string;
  model: string;
  year: number;
  vehicle_type: string;
  created_at: string;
  vehicle_license_plates: {
    license_plate: string;
  }[] | null;
}

export default function VehiclesPage() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState('make_model');
  const router = useRouter();
  const { isAdmin, hasPermission } = usePermissions();

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('vehicles')
        .select(`
          *,
          vehicle_license_plates(license_plate)
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        throw error;
      }

      setVehicles(data || []);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      toast.error('Failed to load vehicles');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchTerm.trim()) {
      fetchVehicles();
      return;
    }

    setLoading(true);
    let query = supabase
      .from('vehicles')
      .select(`
        *,
        vehicle_license_plates(license_plate)
      `);

    if (searchType === 'make_model') {
      // Search by make or model
      query = query.or(`make.ilike.%${searchTerm}%,model.ilike.%${searchTerm}%`);
    } else if (searchType === 'vin') {
      // Search by VIN
      query = query.ilike('vin', `%${searchTerm}%`);
    } else if (searchType === 'plate') {
      // This is more complex as we need to search in a related table
      // First, find vehicle_ids with matching plates
      supabase
        .from('vehicle_license_plates')
        .select('vehicle_id')
        .ilike('license_plate', `%${searchTerm}%`)
        .then(({ data: plateData, error: plateError }) => {
          if (plateError) {
            console.error('Error searching by plate:', plateError);
            toast.error('Error searching by license plate');
            setLoading(false);
            return;
          }

          if (plateData && plateData.length > 0) {
            const vehicleIds = plateData.map(plate => plate.vehicle_id);

            // Then fetch vehicles with those IDs
            supabase
              .from('vehicles')
              .select(`
                *,
                vehicle_license_plates(license_plate)
              `)
              .in('id', vehicleIds)
              .order('created_at', { ascending: false })
              .then(({ data: vehicleData, error: vehicleError }) => {
                if (vehicleError) {
                  console.error('Error fetching vehicles by plate:', vehicleError);
                  toast.error('Error fetching vehicles');
                } else {
                  setVehicles(vehicleData || []);
                }
                setLoading(false);
              });
          } else {
            setVehicles([]);
            setLoading(false);
          }
        });

      return; // Return early as we're handling this case separately
    }

    // Execute the query for non-plate searches
    query
      .order('created_at', { ascending: false })
      .limit(50)
      .then(({ data, error }) => {
        if (error) {
          console.error('Error searching vehicles:', error);
          toast.error('Error searching vehicles');
        } else {
          setVehicles(data || []);
        }
        setLoading(false);
      });
  };

  const getPlateNumber = (vehicle: Vehicle): string => {
    if (vehicle.vehicle_license_plates && vehicle.vehicle_license_plates.length > 0) {
      return vehicle.vehicle_license_plates[0].license_plate;
    }
    return 'N/A';
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Vehicles</h1>
        <div className="flex gap-2">
          <Link href="/dashboard/vehicles/request">
            <Button>Request History</Button>
          </Link>
          {(isAdmin || hasPermission('vehicle:create')) && (
            <Link href="/dashboard/vehicles/new">
              <Button variant="default" className="bg-green-600 hover:bg-green-700">Add Vehicle</Button>
            </Link>
          )}
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Search Vehicles</CardTitle>
          <CardDescription>Find vehicles by make, model, VIN, or license plate</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search term..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="w-full sm:w-48">
              <select
                value={searchType}
                onChange={(e) => setSearchType(e.target.value)}
                className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <option value="make_model">Make/Model</option>
                <option value="vin">VIN</option>
                <option value="plate">License Plate</option>
              </select>
            </div>
            <Button type="submit" disabled={loading}>
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Vehicle List</CardTitle>
          <CardDescription>
            {vehicles.length} {vehicles.length === 1 ? 'vehicle' : 'vehicles'} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : vehicles.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Make & Model</TableHead>
                    <TableHead>Year</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>VIN</TableHead>
                    <TableHead>License Plate</TableHead>
                    <TableHead>Added</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vehicles.map((vehicle) => (
                    <TableRow key={vehicle.id}>
                      <TableCell className="font-medium">
                        {vehicle.make} {vehicle.model}
                      </TableCell>
                      <TableCell>{vehicle.year}</TableCell>
                      <TableCell className="capitalize">{vehicle.vehicle_type}</TableCell>
                      <TableCell className="font-mono text-xs">{vehicle.vin}</TableCell>
                      <TableCell className="font-mono">{getPlateNumber(vehicle)}</TableCell>
                      <TableCell>{formatDate(vehicle.created_at)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Link href={`/dashboard/vehicles/${vehicle.id}`}>
                            <Button variant="outline" size="sm">View</Button>
                          </Link>
                          <Link href={`/dashboard/vehicles/request?vehicle_id=${vehicle.id}`}>
                            <Button size="sm">Request History</Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-neutral-900 mb-2">No vehicles found</h3>
              <p className="text-neutral-500 mb-6">
                {searchTerm
                  ? `No vehicles match your search for "${searchTerm}"`
                  : 'There are no vehicles in the system yet'}
              </p>
              <div className="flex justify-center gap-4">
                <Link href="/dashboard/vehicles/request">
                  <Button>Request History</Button>
                </Link>
                {(isAdmin || hasPermission('vehicle:create')) && (
                  <Link href="/dashboard/vehicles/new">
                    <Button variant="default" className="bg-green-600 hover:bg-green-700">Add Vehicle</Button>
                  </Link>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
