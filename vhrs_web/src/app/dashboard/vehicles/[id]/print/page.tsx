'use client';

import { Button } from '@/components/ui/button';
import { PrintableVehicleHistoryRecord } from '@/components/vehicles/PrintableVehicleHistoryRecord';
import { supabase } from '@/lib/supabase';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function PrintVehicleHistoryPage({ params }: { params: { id: string } }) {
  const [loading, setLoading] = useState(true);
  const [historyData, setHistoryData] = useState<any>(null);

  useEffect(() => {
    fetchVehicleHistory();
  }, [params.id]);

  const fetchVehicleHistory = async () => {
    setLoading(true);
    try {
      // First, check if there's an existing history report
      const { data: historyRequests, error: historyError } = await supabase
        .from('history_report_requests')
        .select('*')
        .eq('vehicle_id', params.id)
        .eq('status', 'completed')
        .order('completed_at', { ascending: false })
        .limit(1);

      if (historyError) throw historyError;

      if (historyRequests && historyRequests.length > 0 && historyRequests[0].history_report) {
        // Use the existing history report
        setHistoryData(historyRequests[0].history_report);
        setLoading(false);
        return;
      }

      // If no existing record, generate a new one
      const response = await fetch(`/api/edge/generate-vehicle-history?vehicle_id=${params.id}`);

      if (!response.ok) {
        throw new Error(`Failed to generate history: ${response.statusText}`);
      }

      const data = await response.json();
      setHistoryData(data);
    } catch (error) {
      console.error('Error fetching vehicle history:', error);
      toast.error('Failed to load vehicle history');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link href={`/dashboard/vehicles/${params.id}`}>
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Printable Vehicle History</h1>
      </div>

      <PrintableVehicleHistoryRecord
        vehicleId={params.id}
        historyData={historyData}
        isLoading={loading}
      />
    </div>
  );
}
