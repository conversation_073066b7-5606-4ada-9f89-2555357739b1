'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

// Define the form schema with validation
const formSchema = z.object({
  make: z.string().min(1, 'Make is required'),
  model: z.string().min(1, 'Model is required'),
  year: z.coerce
    .number()
    .int()
    .min(1900, 'Year must be 1900 or later')
    .max(new Date().getFullYear() + 1, `Year cannot be later than ${new Date().getFullYear() + 1}`),
  vehicle_type: z.enum(['car', 'motorcycle', 'truck'], {
    required_error: 'Please select a vehicle type',
  }),
  vin: z.string().min(1, 'VIN is required'),
  license_plate: z.string().min(1, 'License plate number is required'),
  registration_centre: z.string().optional(),
  color: z.string().min(1, 'Color is required'),
  owner_name: z.string().min(1, 'Owner name is required'),
  owner_phone: z.string().min(1, 'Owner phone is required'),
});

export default function NewVehiclePage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { isAdmin, hasPermission, loading: permissionsLoading } = usePermissions();

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      make: '',
      model: '',
      year: new Date().getFullYear(),
      vehicle_type: 'car',
      vin: '',
      license_plate: '',
      registration_centre: '',
      color: '',
      owner_name: '',
      owner_phone: '',
    },
  });

  useEffect(() => {
    // Redirect if user doesn't have permission
    if (!permissionsLoading && !isAdmin && !hasPermission('vehicle:create')) {
      toast.error('You do not have permission to add new vehicles');
      router.push('/dashboard/vehicles');
    }
  }, [isAdmin, hasPermission, permissionsLoading, router]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    setError(null);

    try {
      // 1. Insert the vehicle
      const { data: vehicleData, error: vehicleError } = await supabase
        .from('vehicles')
        .insert({
          make: values.make,
          model: values.model,
          year: values.year,
          vehicle_type: values.vehicle_type,
          vin: values.vin,
        })
        .select('id')
        .single();

      if (vehicleError) throw vehicleError;

      const vehicleId = vehicleData.id;

      // 2. Insert the license plate
      const { error: plateError } = await supabase
        .from('vehicle_license_plates')
        .insert({
          vehicle_id: vehicleId,
          license_plate: values.license_plate,
          registration_centre: values.registration_centre || null,
          start_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
        });

      if (plateError) throw plateError;

      // 3. Insert the vehicle color
      const { error: colorError } = await supabase
        .from('vehicle_colors')
        .insert({
          vehicle_id: vehicleId,
          color: values.color,
          effective_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
        });

      if (colorError) throw colorError;

      // 4. Insert the ownership record
      const { error: ownershipError } = await supabase
        .from('ownership_history')
        .insert({
          vehicle_id: vehicleId,
          owner_name: values.owner_name,
          owner_phone: values.owner_phone,
          start_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
          is_current: true,
        });

      if (ownershipError) throw ownershipError;

      // Show success message
      toast.success('Vehicle added successfully');

      // Redirect to the vehicle detail page
      router.push(`/dashboard/vehicles/${vehicleId}`);
    } catch (err: any) {
      console.error('Error creating vehicle:', err);
      setError(err.message || 'An error occurred while creating the vehicle');
      toast.error('Failed to add vehicle');
      setLoading(false);
    }
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  if (!isAdmin && !hasPermission('vehicle:create')) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">Access Denied</h2>
        <p className="text-neutral-600 mb-4">You do not have permission to add new vehicles.</p>
        <Link href="/dashboard/vehicles">
          <Button>Back to Vehicles</Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Add New Vehicle</h1>
        <Link href="/dashboard/vehicles">
          <Button variant="outline">Back to Vehicles</Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Vehicle Information</CardTitle>
          <CardDescription>
            Enter the details for the new vehicle. Fields marked with * are required.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Basic Vehicle Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="make"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Make *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Toyota" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="model"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Corolla" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="year"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Year *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="e.g., 2020"
                            {...field}
                            onChange={(e) => field.onChange(e.target.valueAsNumber)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicle_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Vehicle Type *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select vehicle type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="car">Car</SelectItem>
                            <SelectItem value="motorcycle">Motorcycle</SelectItem>
                            <SelectItem value="truck">Truck</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vin"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>VIN (Vehicle Identification Number) *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., 1HGCM82633A123456" {...field} />
                        </FormControl>
                        <FormDescription>
                          The unique identifier for this vehicle.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <h3 className="text-lg font-medium mt-6">License Plate Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="license_plate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>License Plate Number *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., UAX 123K" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="registration_centre"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Registration Centre</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., URA Kampala" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <h3 className="text-lg font-medium mt-6">Vehicle Appearance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="color"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Red" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <h3 className="text-lg font-medium mt-6">Ownership Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="owner_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Owner Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., John Doe" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="owner_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Owner Phone *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., +256 700 123456" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                  {error}
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => router.push('/dashboard/vehicles')} disabled={loading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Vehicle'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}