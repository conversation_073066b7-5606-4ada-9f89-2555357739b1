import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import NewVehiclePage from './page';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';

// Mock the hooks and supabase client
jest.mock('@/hooks/usePermissions');
jest.mock('@/lib/supabase');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

describe('NewVehiclePage Component', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (usePermissions as jest.Mock).mockReturnValue({
      isAdmin: true,
      isDataAgent: false,
      loading: false,
    });
    
    // Mock Supabase responses
    (supabase.from as jest.Mock).mockReturnValue({
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: { id: 'test-vehicle-id' },
        error: null,
      }),
    });
  });

  test('renders loading state when permissions are loading', () => {
    (usePermissions as jest.Mock).mockReturnValue({
      isAdmin: false,
      isDataAgent: false,
      loading: true,
    });

    render(<NewVehiclePage />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  test('redirects non-admin/non-data-agent users', async () => {
    (usePermissions as jest.Mock).mockReturnValue({
      isAdmin: false,
      isDataAgent: false,
      loading: false,
    });

    render(<NewVehiclePage />);
    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(mockRouter.push).toHaveBeenCalledWith('/dashboard/vehicles');
  });

  test('renders the form for admin users', async () => {
    render(<NewVehiclePage />);
    
    await waitFor(() => {
      expect(screen.getByText('Add New Vehicle')).toBeInTheDocument();
      expect(screen.getByText('Vehicle Information')).toBeInTheDocument();
    });
    
    // Check for form sections
    expect(screen.getByText('Basic Vehicle Information')).toBeInTheDocument();
    expect(screen.getByText('License Plate Information')).toBeInTheDocument();
    expect(screen.getByText('Vehicle Appearance')).toBeInTheDocument();
    expect(screen.getByText('Ownership Information')).toBeInTheDocument();
  });

  test('submits the form with valid data', async () => {
    render(<NewVehiclePage />);
    
    // Fill out the form
    fireEvent.change(screen.getByLabelText(/Make/i), { target: { value: 'Toyota' } });
    fireEvent.change(screen.getByLabelText(/Model/i), { target: { value: 'Corolla' } });
    fireEvent.change(screen.getByLabelText(/Year/i), { target: { value: '2020' } });
    
    // Select vehicle type
    fireEvent.click(screen.getByText('Select vehicle type'));
    fireEvent.click(screen.getByText('Car'));
    
    fireEvent.change(screen.getByLabelText(/VIN/i), { target: { value: '1HGCM82633A123456' } });
    fireEvent.change(screen.getByLabelText(/License Plate Number/i), { target: { value: 'ABC123' } });
    fireEvent.change(screen.getByLabelText(/Registration Centre/i), { target: { value: 'URA Kampala' } });
    fireEvent.change(screen.getByLabelText(/Color/i), { target: { value: 'Red' } });
    fireEvent.change(screen.getByLabelText(/Owner Name/i), { target: { value: 'John Doe' } });
    fireEvent.change(screen.getByLabelText(/Owner Phone/i), { target: { value: '+256 700 123456' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('Create Vehicle'));
    
    // Check that the API was called
    await waitFor(() => {
      expect(supabase.from).toHaveBeenCalledWith('vehicles');
      expect(supabase.from).toHaveBeenCalledWith('vehicle_license_plates');
      expect(supabase.from).toHaveBeenCalledWith('vehicle_colors');
      expect(supabase.from).toHaveBeenCalledWith('ownership_history');
    });
    
    // Check that we were redirected
    expect(mockRouter.push).toHaveBeenCalledWith('/dashboard/vehicles/test-vehicle-id');
  });

  test('shows error message when API call fails', async () => {
    // Mock an error response
    (supabase.from as jest.Mock).mockReturnValueOnce({
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      }),
    });
    
    render(<NewVehiclePage />);
    
    // Fill out the form (minimal required fields)
    fireEvent.change(screen.getByLabelText(/Make/i), { target: { value: 'Toyota' } });
    fireEvent.change(screen.getByLabelText(/Model/i), { target: { value: 'Corolla' } });
    fireEvent.change(screen.getByLabelText(/VIN/i), { target: { value: '1HGCM82633A123456' } });
    fireEvent.change(screen.getByLabelText(/License Plate Number/i), { target: { value: 'ABC123' } });
    fireEvent.change(screen.getByLabelText(/Color/i), { target: { value: 'Red' } });
    fireEvent.change(screen.getByLabelText(/Owner Name/i), { target: { value: 'John Doe' } });
    fireEvent.change(screen.getByLabelText(/Owner Phone/i), { target: { value: '+256 700 123456' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('Create Vehicle'));
    
    // Check that error message is displayed
    await waitFor(() => {
      expect(screen.getByText('Database error')).toBeInTheDocument();
    });
  });
});
