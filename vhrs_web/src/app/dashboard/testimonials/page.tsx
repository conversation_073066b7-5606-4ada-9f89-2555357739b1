'use client';

import { DynamicTitle } from '@/components/layout/DynamicTitle';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { CheckCircle, MessageSquare, MessageSquarePlus, PlusCircle, Search, XCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

interface Testimonial {
  id: string;
  full_name: string;
  context: string;
  experience: string;
  status: 'pending' | 'approved' | 'rejected';
  is_featured: boolean;
  created_at: string;
  profiles?: {
    full_name: string;
    email: string;
  };
}

// Form validation schema
const feedbackSchema = z.object({
  full_name: z.string().min(2, 'Name must be at least 2 characters'),
  context: z.string().optional(),
  experience: z.string().min(10, 'Your experience must be at least 10 characters').max(500, 'Your experience must be less than 500 characters'),
});

type FeedbackFormValues = z.infer<typeof feedbackSchema>;

export default function TestimonialsPage() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTestimonial, setSelectedTestimonial] = useState<Testimonial | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { hasPermission, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    // Only check permissions after loading is complete
    if (permissionsLoading) {
      return;
    }

    // Check if user has permission to manage testimonials
    if (!hasPermission('testimonials:edit')) {
      router.push('/dashboard');
      toast.error('You do not have permission to access this page');
      return;
    }

    // Fetch testimonials if we have permission
    fetchTestimonials();
  }, [permissionsLoading, hasPermission, router]);

  const fetchTestimonials = async () => {
    try {
      setLoading(true);

      // Use direct database query
      const { data: dbData, error: dbError } = await supabase
        .from('testimonials')
        .select('*, profiles!testimonials_user_id_fkey(full_name, email)')
        .order('created_at', { ascending: false });

      if (dbError) {
        console.error('Error fetching testimonials from database:', dbError);
        throw dbError;
      }

      setTestimonials(dbData || []);
    } catch (err) {
      console.error('Error fetching testimonials:', err);
      toast.error('Failed to load testimonials');
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (action: 'approve' | 'reject' | 'feature', testimonialId: string) => {
    if (!testimonialId) return;

    try {
      setIsProcessing(true);

      // Use direct database queries based on the action
      if (action === 'approve') {
        const { error } = await supabase
          .from('testimonials')
          .update({
            status: 'approved',
            approved_at: new Date().toISOString(),
            approved_by: user?.id
          })
          .eq('id', testimonialId);

        if (error) throw error;
      }
      else if (action === 'reject') {
        const { error } = await supabase
          .from('testimonials')
          .update({
            status: 'rejected',
            approved_at: new Date().toISOString(),
            approved_by: user?.id
          })
          .eq('id', testimonialId);

        if (error) throw error;
      }
      else if (action === 'feature') {
        // Get current featured status
        const { data: currentData, error: currentError } = await supabase
          .from('testimonials')
          .select('is_featured')
          .eq('id', testimonialId)
          .single();

        if (currentError) throw currentError;

        // Toggle the featured status
        const { error } = await supabase
          .from('testimonials')
          .update({
            is_featured: !currentData.is_featured
          })
          .eq('id', testimonialId);

        if (error) throw error;
      }

      // Update local state based on action
      setTestimonials(prev => prev.map(t => {
        if (t.id === testimonialId) {
          if (action === 'approve') {
            return { ...t, status: 'approved' };
          } else if (action === 'reject') {
            return { ...t, status: 'rejected' };
          } else if (action === 'feature') {
            return { ...t, is_featured: !t.is_featured };
          }
        }
        return t;
      }));

      toast.success(`Testimonial ${action === 'feature' ? 'featured status updated' : action + 'd'} successfully`);
      setDialogOpen(false);
    } catch (err) {
      console.error(`Error ${action}ing testimonial:`, err);
      toast.error(`Failed to ${action} testimonial`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Filter testimonials based on search query and status filter
  const filteredTestimonials = testimonials.filter(testimonial => {
    const matchesSearch =
      testimonial.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      testimonial.experience.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (testimonial.context && testimonial.context.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = statusFilter === 'all' || testimonial.status === statusFilter;

    return matchesSearch && matchesStatus;
  });



  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Pending</span>;
      case 'approved':
        return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Approved</span>;
      case 'rejected':
        return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Rejected</span>;
      default:
        return null;
    }
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  if (!hasPermission('testimonials:edit')) {
    return null; // Router will redirect
  }

  return (
    <div className="container mx-auto py-6">
      <DynamicTitle title="Testimonial Management" />

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Testimonial Management</h1>
          <p className="text-muted-foreground">
            Review and manage user testimonials and feedback
          </p>
        </div>
        <div className="flex flex-col md:flex-row gap-3 mt-4 md:mt-0">
          {/* Share Experience Button - For regular users only (not admins) */}
          {!hasPermission('testimonials:edit') && (
            <Button
              onClick={() => setFeedbackDialogOpen(true)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <MessageSquarePlus className="h-4 w-4" />
              <span>Share Your Experience</span>
            </Button>
          )}

          {/* Create Testimonial Button - For admins only */}
          {hasPermission('testimonials:edit') && (
            <Button
              onClick={() => setCreateDialogOpen(true)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <PlusCircle className="h-4 w-4" />
              <span>Create Testimonial</span>
            </Button>
          )}

          <Button
            onClick={fetchTestimonials}
            variant="outline"
          >
            Refresh
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Filter Testimonials</CardTitle>
          <CardDescription>
            Search and filter testimonials by status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search testimonials..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
            >
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Testimonials</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="featured">Featured</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0">
          {renderTestimonialsList(filteredTestimonials)}
        </TabsContent>

        <TabsContent value="pending" className="mt-0">
          {renderTestimonialsList(filteredTestimonials.filter(t => t.status === 'pending'))}
        </TabsContent>

        <TabsContent value="approved" className="mt-0">
          {renderTestimonialsList(filteredTestimonials.filter(t => t.status === 'approved'))}
        </TabsContent>

        <TabsContent value="featured" className="mt-0">
          {renderTestimonialsList(filteredTestimonials.filter(t => t.is_featured))}
        </TabsContent>
      </Tabs>

      {/* Testimonial Detail Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Testimonial Details</DialogTitle>
            <DialogDescription>
              Review and manage this testimonial
            </DialogDescription>
          </DialogHeader>

          {selectedTestimonial && (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">From</h3>
                <p className="font-semibold">{selectedTestimonial.full_name}</p>
                <p className="text-sm text-muted-foreground">{selectedTestimonial.context}</p>
                {selectedTestimonial.profiles?.email && (
                  <p className="text-sm text-muted-foreground">{selectedTestimonial.profiles.email}</p>
                )}
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Experience</h3>
                <p className="text-sm mt-1">{selectedTestimonial.experience}</p>
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Status</h3>
                <div className="mt-1">{renderStatusBadge(selectedTestimonial.status)}</div>
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Submitted</h3>
                <p className="text-sm">{formatDate(selectedTestimonial.created_at)}</p>
              </div>
            </div>
          )}

          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            {selectedTestimonial?.status === 'pending' && (
              <>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto"
                  onClick={() => handleAction('reject', selectedTestimonial.id)}
                  disabled={isProcessing}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
                <Button
                  variant="default"
                  className="w-full sm:w-auto"
                  onClick={() => handleAction('approve', selectedTestimonial.id)}
                  disabled={isProcessing}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve
                </Button>
              </>
            )}

            {selectedTestimonial?.status === 'approved' && (
              <Button
                variant={selectedTestimonial.is_featured ? "outline" : "default"}
                className="w-full sm:w-auto"
                onClick={() => handleAction('feature', selectedTestimonial.id)}
                disabled={isProcessing}
              >
                {selectedTestimonial.is_featured ? 'Remove from Featured' : 'Add to Featured'}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Share Your Experience Dialog */}
      <Dialog open={feedbackDialogOpen} onOpenChange={setFeedbackDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Share Your Experience</DialogTitle>
            <DialogDescription>
              Tell us about your experience with our vehicle history record system. Your feedback helps us improve and helps others make informed decisions.
            </DialogDescription>
          </DialogHeader>

          <FeedbackForm />
        </DialogContent>
      </Dialog>

      {/* Admin Create Testimonial Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create Testimonial</DialogTitle>
            <DialogDescription>
              Create a new testimonial entry. This will be marked as approved automatically.
            </DialogDescription>
          </DialogHeader>

          <AdminCreateTestimonialForm onSuccess={() => {
            setCreateDialogOpen(false);
            fetchTestimonials();
          }} />
        </DialogContent>
      </Dialog>
    </div>
  );

  // Feedback Form Component
  function FeedbackForm() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { user } = useAuth();

    // Initialize form with react-hook-form
    const form = useForm<FeedbackFormValues>({
      resolver: zodResolver(feedbackSchema),
      defaultValues: {
        full_name: user?.user_metadata?.full_name || '',
        context: '',
        experience: '',
      },
    });

    // Handle form submission
    const onSubmit = async (values: FeedbackFormValues) => {
      if (!user) {
        toast.error('You must be logged in to submit feedback');
        return;
      }

      try {
        setIsSubmitting(true);

        // Use direct database insertion
        const { error: insertError } = await supabase
          .from('testimonials')
          .insert({
            user_id: user.id,
            full_name: values.full_name,
            context: values.context,
            experience: values.experience,
            status: 'pending',
          });

        if (insertError) {
          throw insertError;
        }

        toast.success('Thank you for your feedback!');
        setFeedbackDialogOpen(false);
        form.reset();
        fetchTestimonials();
      } catch (err) {
        console.error('Error submitting feedback:', err);
        toast.error('Failed to submit feedback. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Your Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter your name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="context"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Context (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Car Buyer, Broker, Kampala" {...field} />
                </FormControl>
                <FormDescription>
                  This helps provide context to your feedback
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="experience"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Your Experience</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Share your experience with our system..."
                    className="min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Maximum 500 characters
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setFeedbackDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </Button>
          </DialogFooter>
        </form>
      </Form>
    );
  }

  // Admin Create Testimonial Form
  function AdminCreateTestimonialForm({ onSuccess }: { onSuccess: () => void }) {
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Create a schema with additional fields for admin creation
    const adminCreateSchema = z.object({
      full_name: z.string().min(2, 'Name must be at least 2 characters'),
      context: z.string().min(2, 'Context is required for admin-created testimonials'),
      experience: z.string().min(10, 'Experience must be at least 10 characters').max(500, 'Experience must be less than 500 characters'),
      is_featured: z.boolean().default(false),
    });

    type AdminCreateFormValues = z.infer<typeof adminCreateSchema>;

    // Initialize form with react-hook-form
    const form = useForm<AdminCreateFormValues>({
      resolver: zodResolver(adminCreateSchema),
      defaultValues: {
        full_name: '',
        context: '',
        experience: '',
        is_featured: false,
      },
    });

    // Handle form submission
    const onSubmit = async (values: AdminCreateFormValues) => {
      try {
        setIsSubmitting(true);

        // Use direct database insertion
        const { error: insertError } = await supabase
          .from('testimonials')
          .insert({
            full_name: values.full_name,
            context: values.context,
            experience: values.experience,
            is_featured: values.is_featured,
            status: 'approved', // Auto-approve admin-created testimonials
            approved_at: new Date().toISOString(),
            approved_by: user?.id
          });

        if (insertError) {
          throw insertError;
        }

        toast.success('Testimonial created successfully');
        form.reset();
        onSuccess();
      } catch (err) {
        console.error('Error creating testimonial:', err);
        toast.error('Failed to create testimonial. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="context"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Context</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Car Buyer, Broker, Kampala" {...field} />
                </FormControl>
                <FormDescription>
                  Provide context like role and location
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="experience"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Experience</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter user experience..."
                    className="min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Maximum 500 characters
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="is_featured"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Feature this testimonial</FormLabel>
                  <FormDescription>
                    Featured testimonials appear on the landing page
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Testimonial'}
            </Button>
          </DialogFooter>
        </form>
      </Form>
    );
  }

  // Helper function to render testimonials list
  function renderTestimonialsList(testimonials: Testimonial[]) {
    if (loading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-8 w-20 rounded-full" />
                </div>
                <div className="mt-4 space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
                <div className="mt-4 flex justify-between items-center">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-9 w-20 rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    if (testimonials.length === 0) {
      return (
        <Card>
          <CardContent className="p-6 text-center">
            <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground opacity-50" />
            <h3 className="mt-4 text-lg font-medium">No testimonials found</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {statusFilter !== 'all'
                ? `No testimonials with status "${statusFilter}" found.`
                : searchQuery
                ? `No testimonials matching "${searchQuery}" found.`
                : 'There are no testimonials to display yet.'}
            </p>
          </CardContent>
        </Card>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {testimonials.map((testimonial) => (
          <Card key={testimonial.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold">{testimonial.full_name}</h3>
                  <p className="text-sm text-muted-foreground">{testimonial.context}</p>
                </div>
                <div className="flex items-center gap-2">
                  {renderStatusBadge(testimonial.status)}
                  {testimonial.is_featured && (
                    <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Featured</span>
                  )}
                </div>
              </div>



              <p className="mt-4 text-sm line-clamp-3">{testimonial.experience}</p>

              <div className="mt-4 flex justify-between items-center">
                <span className="text-xs text-muted-foreground">
                  {formatDate(testimonial.created_at)}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedTestimonial(testimonial);
                    setDialogOpen(true);
                  }}
                >
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }
}
