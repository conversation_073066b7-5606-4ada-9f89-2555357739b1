'use client';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ResponsiveTable } from '@/components/ui/responsive-table';
import { RoleChangeDialog } from '@/components/users/RoleChangeDialog';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

// Define the User type
type User = {
  id: string;
  email: string;
  full_name: string | null;
  is_verified: boolean;
  created_at: string;
  roles: string[];
};

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const { hasPermission, isAdmin, loading: permissionsLoading } = usePermissions();
  const router = useRouter();

  useEffect(() => {
    // Only check permissions after loading is complete
    if (permissionsLoading) {
      return;
    }

    // Check if user has permission to manage users
    if (!hasPermission('user:manage')) {
      router.push('/dashboard');
      toast.error('You do not have permission to access this page');
      return;
    }

    // Fetch users if we have permission
    fetchUsers();
  }, [permissionsLoading, hasPermission, router]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Use the user_with_roles view to get users with their roles
      const { data: usersWithRoles, error: viewError } = await supabase
        .from('user_with_roles')
        .select('*')
        .order('created_at', { ascending: false });

      if (viewError) throw viewError;

      setUsers(usersWithRoles || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  // Define columns for the data table
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'full_name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => <div className="font-medium">{row.getValue('full_name') || 'N/A'}</div>,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Email
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: 'roles',
      header: 'Role',
      cell: ({ row }) => {
        const roles = row.getValue('roles') as string[];
        const primaryRole = roles && roles.length > 0 ? roles[0] : 'user';

        return (
          <Badge variant={primaryRole === 'admin' ? 'destructive' : 'secondary'}>
            {primaryRole}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'is_verified',
      header: 'Status',
      cell: ({ row }) => {
        const isVerified = row.getValue('is_verified') as boolean;
        return (
          <Badge variant={isVerified ? 'outline' : 'secondary'}>
            {isVerified ? 'Verified' : 'Unverified'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue('created_at'));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const user = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/users/${user.id}`}>View Details</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/users/${user.id}/edit`}>Edit User</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <RoleChangeDialog user={user} onRoleChange={fetchUsers} />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return null; // Router will redirect
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Link href="/dashboard/users/new">
          <Button>Add New User</Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>Manage system users and their roles</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveTable
            columns={columns}
            data={users}
            searchColumn="email"
            searchPlaceholder="Search by email..."
            loading={loading}
            mobileCardFields={{
              title: 'full_name',
              subtitle: 'email',
              fields: [
                { key: 'roles', label: 'Role' },
                { key: 'is_verified', label: 'Status' },
                { key: 'created_at', label: 'Created' },
              ],
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
