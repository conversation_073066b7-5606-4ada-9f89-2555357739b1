'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';
import { ArrowLeft, Clock, FileText, RefreshCw, Shield, UserCheck } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

type UserActivity = {
  id: string;
  type: string;
  date: string;
  details: string;
  metadata?: any;
};

type UserDetails = {
  id: string;
  email: string;
  full_name: string | null;
  is_verified: boolean;
  created_at: string;
  last_active_at: string | null;
  roles: string[];
};

export default function UserActivityPage() {
  const [user, setUser] = useState<UserDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [activityLoading, setActivityLoading] = useState(true);
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const { hasPermission, loading: permissionsLoading } = usePermissions();
  const router = useRouter();

  // Use the Next.js useParams hook to get the ID from the route
  const params = useParams();
  const userId = typeof params.id === 'string' ? params.id : '';

  useEffect(() => {
    if (!permissionsLoading && !hasPermission('user:manage')) {
      router.push('/dashboard');
      toast.error('You do not have permission to access this page');
      return;
    }

    fetchUser();
    fetchUserActivity();
  }, [userId, permissionsLoading, hasPermission, router]);

  const fetchUser = async () => {
    setLoading(true);
    try {
      // Get user data
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError) throw userError;

      // Get user roles
      const { data: userRolesData, error: rolesError } = await supabase
        .from('user_roles')
        .select('roles(name)')
        .eq('user_id', userId);

      if (rolesError) throw rolesError;

      // Extract role names
      const roles = userRolesData
        ? userRolesData.map((ur) => (ur.roles ? ur.roles.name : 'user'))
        : ['user'];

      setUser({
        ...userData,
        roles,
      });
    } catch (error) {
      console.error('Error fetching user:', error);
      toast.error('Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserActivity = async () => {
    setActivityLoading(true);
    try {
      // Fetch login activity (mock data for now)
      const mockLoginEvents = [
        {
          id: 'login-1',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
          ip_address: '***********',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
        {
          id: 'login-2',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
          ip_address: '***********',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        },
      ];

      // Get recent actions (alerts, history requests, data submissions)
      const [alertsRes, requestsRes, submissionsRes] = await Promise.all([
        supabase
          .from('alerts')
          .select('id, created_at, alert_type_id, status, description')
          .eq('created_by', userId)
          .order('created_at', { ascending: false })
          .limit(10),
        supabase
          .from('history_report_requests')
          .select('id, created_at, status, vehicle_id, vehicles(make, model, year)')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(10),
        supabase
          .from('data_submissions')
          .select('id, timestamp, action, table_name, record_id, old_data, new_data')
          .eq('user_id', userId)
          .order('timestamp', { ascending: false })
          .limit(10),
      ]);

      // Format login events
      const loginEvents = mockLoginEvents.map((event) => ({
        id: event.id,
        type: 'login',
        date: event.created_at,
        details: `Login from ${event.ip_address}`,
        metadata: {
          ip_address: event.ip_address,
          user_agent: event.user_agent,
        },
      }));

      // Format alert events
      const alertEvents = (alertsRes.data || []).map((alert) => ({
        id: `alert-${alert.id}`,
        type: 'alert',
        date: alert.created_at,
        details: `Submitted alert: ${alert.description?.substring(0, 50)}${
          alert.description && alert.description.length > 50 ? '...' : ''
        } (${alert.status})`,
        metadata: {
          alert_id: alert.id,
          alert_type: alert.alert_type_id,
          status: alert.status,
        },
      }));

      // Format history request events
      const requestEvents = (requestsRes.data || []).map((request) => ({
        id: `request-${request.id}`,
        type: 'history_request',
        date: request.created_at,
        details: `Requested history record for ${
          request.vehicles
            ? `${request.vehicles.make} ${request.vehicles.model} (${request.vehicles.year})`
            : 'a vehicle'
        } (${request.status})`,
        metadata: {
          request_id: request.id,
          vehicle_id: request.vehicle_id,
          status: request.status,
        },
      }));

      // Format data submission events
      const submissionEvents = (submissionsRes.data || []).map((submission) => {
        let details = '';
        switch (submission.action) {
          case 'INSERT':
            details = `Created new ${submission.table_name} record`;
            break;
          case 'UPDATE':
            details = `Updated ${submission.table_name} record`;
            break;
          case 'DELETE':
            details = `Deleted ${submission.table_name} record`;
            break;
          default:
            details = `Modified ${submission.table_name} record`;
        }

        return {
          id: `submission-${submission.id}`,
          type: 'data_submission',
          date: submission.timestamp,
          details,
          metadata: {
            submission_id: submission.id,
            table_name: submission.table_name,
            record_id: submission.record_id,
            action: submission.action,
            old_data: submission.old_data,
            new_data: submission.new_data,
          },
        };
      });

      // Combine all events, sort by date (newest first)
      const allActivity = [...loginEvents, ...alertEvents, ...requestEvents, ...submissionEvents].sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      setActivities(allActivity);
    } catch (error) {
      console.error('Error fetching user activities:', error);
      toast.error('Failed to load user activity');
    } finally {
      setActivityLoading(false);
    }
  };

  const getFilteredActivities = () => {
    if (activeTab === 'all') return activities;
    return activities.filter((activity) => activity.type === activeTab);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <Shield className="h-4 w-4 text-blue-500" />;
      case 'alert':
        return <UserCheck className="h-4 w-4 text-red-500" />;
      case 'history_request':
        return <FileText className="h-4 w-4 text-green-500" />;
      case 'data_submission':
        return <Clock className="h-4 w-4 text-amber-500" />;
      default:
        return <Clock className="h-4 w-4 text-neutral-500" />;
    }
  };

  if (permissionsLoading || loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">User Not Found</h2>
        <p className="text-neutral-500 mb-4">The requested user could not be found.</p>
        <Link href="/dashboard/users">
          <Button>Back to Users</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link href={`/dashboard/users/${userId}`}>
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">User Activity</h1>
        </div>
        <Button onClick={fetchUserActivity} variant="outline" className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Activity for {user.full_name || user.email}</CardTitle>
          <CardDescription>
            View all activity for this user including logins, alerts, history requests, and data changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-4">
              <TabsTrigger value="all">All Activity</TabsTrigger>
              <TabsTrigger value="login">Logins</TabsTrigger>
              <TabsTrigger value="alert">Alerts</TabsTrigger>
              <TabsTrigger value="history_request">History Requests</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-0">
              {activityLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-500"></div>
                </div>
              ) : getFilteredActivities().length > 0 ? (
                <div className="space-y-4">
                  {getFilteredActivities().map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-start gap-4 p-4 border rounded-lg hover:bg-neutral-50 transition-colors"
                    >
                      <div className="mt-0.5">{getActivityIcon(activity.type)}</div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium">{activity.details}</p>
                        <p className="text-sm text-neutral-500">{formatDate(activity.date)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-neutral-500">
                  No activity found for this user in the selected category
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
