'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DeleteUserDialog } from '@/components/users/DeleteUserDialog';
import { RoleChangeDialog } from '@/components/users/RoleChangeDialog';
import { VerificationControls } from '@/components/users/VerificationControls';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { AlertCircle, CheckCircle, Clock, User } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

type UserDetails = {
  id: string;
  email: string;
  full_name: string | null;
  is_verified: boolean;
  created_at: string;
  last_active_at: string | null;
  roles: string[];
};

export default function UserDetailPage() {
  const [user, setUser] = useState<UserDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const { hasPermission, loading: permissionsLoading } = usePermissions();
  const router = useRouter();

  // Use the Next.js useParams hook to get the ID from the route
  const params = useParams();
  const userId = typeof params.id === 'string' ? params.id : '';

  useEffect(() => {
    if (!permissionsLoading && !hasPermission('user:manage')) {
      router.push('/dashboard');
      toast.error('You do not have permission to access this page');
      return;
    }

    fetchUser();
  }, [userId, permissionsLoading, hasPermission, router]);

  const fetchUser = async () => {
    setLoading(true);
    try {
      // Get user with roles
      const { data, error } = await supabase
        .from('user_with_roles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      setUser(data);
    } catch (error) {
      console.error('Error fetching user:', error);
      toast.error('Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  if (permissionsLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-7 w-40 mb-2" />
            <Skeleton className="h-5 w-64" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!hasPermission('user:manage')) {
    return null; // Router will redirect
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">User Not Found</h2>
        <p className="text-neutral-600 mb-4">The requested user could not be found.</p>
        <Link href="/dashboard/users">
          <Button>Back to Users</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Details</h1>
        <div className="flex gap-2">
          <Link href="/dashboard/users">
            <Button variant="outline">Back to Users</Button>
          </Link>
          <Link href={`/dashboard/users/${user.id}/edit`}>
            <Button>Edit User</Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{user.full_name || 'Unnamed User'}</CardTitle>
              <CardDescription>{user.email}</CardDescription>
            </div>
            <div className="flex flex-col gap-2 items-end">
              <Badge variant={user.is_verified ? 'outline' : 'secondary'}>
                {user.is_verified ? 'Verified' : 'Unverified'}
              </Badge>
              {user.roles && user.roles.length > 0 && (
                <Badge variant={user.roles[0] === 'admin' ? 'destructive' : user.roles[0] === 'data_agent' ? 'default' : 'secondary'}>
                  {user.roles[0]}
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">User Details</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div className="flex items-center gap-2 p-4 border rounded-md">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Full Name</p>
                    <p className="text-sm text-muted-foreground">{user.full_name || 'Not provided'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 p-4 border rounded-md">
                  <CheckCircle className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Verification Status</p>
                    <p className="text-sm text-muted-foreground">{user.is_verified ? 'Verified' : 'Not verified'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 p-4 border rounded-md">
                  <Clock className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Account Created</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(user.created_at).toLocaleDateString()} at {new Date(user.created_at).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 p-4 border rounded-md">
                  <AlertCircle className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Role</p>
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-muted-foreground">
                        {user.roles && user.roles.length > 0 ? user.roles[0] : 'No role assigned'}
                      </p>
                      <RoleChangeDialog user={user} onRoleChange={fetchUser} />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="activity">
              <div className="space-y-4 mt-4">
                <div className="p-4 border rounded-md">
                  <p className="text-sm text-muted-foreground">
                    Last active: {user.last_active_at ? new Date(user.last_active_at).toLocaleString() : 'Never'}
                  </p>
                </div>
                <div className="flex justify-center">
                  <Link href={`/dashboard/users/${user.id}/activity`}>
                    <Button>View Detailed Activity</Button>
                  </Link>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-muted-foreground">User ID: {user.id}</p>
          <div className="flex gap-2">
            <VerificationControls
              userId={user.id}
              userEmail={user.email}
              isVerified={user.is_verified}
              onVerificationChange={fetchUser}
            />
            <DeleteUserDialog userId={user.id} userEmail={user.email} />
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
