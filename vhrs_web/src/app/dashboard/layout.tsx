'use client';

import { MaintenanceMode } from '@/components/layout/MaintenanceMode';
import { MobileSidebar } from '@/components/layout/MobileSidebar';
import { Navbar } from '@/components/layout/Navbar';
import { Sidebar } from '@/components/layout/Sidebar';
import { SystemContactInfo } from '@/components/layout/SystemContactInfo';
import { Toaster } from '@/components/ui/sonner';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen bg-neutral-50">
      {/* Desktop Sidebar - hidden on mobile */}
      <div className="hidden md:block">
        <Sidebar />
      </div>

      <div className="flex flex-1 flex-col overflow-hidden">
        <div className="flex items-center border-b bg-white px-4 h-14">
          {/* Mobile Sidebar - visible only on mobile */}
          <div className="md:hidden">
            <MobileSidebar />
          </div>

          <Navbar />
        </div>
        <main className="flex-1 overflow-auto p-4 md:p-6">
          <MaintenanceMode />
          {children}
        </main>
        <footer className="border-t bg-white p-4 md:p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <SystemContactInfo />
            <div className="text-sm text-neutral-500">
              &copy; {new Date().getFullYear()} Vehicle History Record System
            </div>
          </div>
        </footer>
      </div>
      <Toaster />
    </div>
  );
}
