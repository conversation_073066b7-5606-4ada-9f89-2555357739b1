'use client';

import { ActivityTimeline } from '@/components/charts/ActivityTimeline';
import { AlertStatusChart } from '@/components/charts/AlertStatusChart';
import { VehicleStatsChart } from '@/components/charts/VehicleStatsChart';
import { StatsCard } from '@/components/dashboard/StatsCard';
import { DynamicTitle } from '@/components/layout/DynamicTitle';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { AlertTriangle, Car, FileText, Users } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function Dashboard() {
  const { user } = useAuth();
  const { isAdmin, hasPermission, userId } = usePermissions();
  const [greeting, setGreeting] = useState('');
  const [stats, setStats] = useState({
    totalVehicles: 0,
    totalAlerts: 0,
    pendingAlerts: 0,
    historyRequests: 0,
    totalUsers: 0,
    // Previous period stats for comparison
    previousTotalVehicles: 0,
    previousTotalAlerts: 0,
    previousPendingAlerts: 0,
    previousHistoryRequests: 0,
    previousTotalUsers: 0,
  });
  const [recentRequests, setRecentRequests] = useState<any[]>([]);
  const [recentAlerts, setRecentAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Set greeting based on time of day
  useEffect(() => {
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good morning');
    } else if (hour < 18) {
      setGreeting('Good afternoon');
    } else {
      setGreeting('Good evening');
    }
  }, []);

  // Fetch dashboard data
  useEffect(() => {
    async function fetchDashboardData() {
      if (!userId) return;

      setLoading(true);
      try {
        // Get current date and date 30 days ago for comparison
        const currentDate = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(currentDate.getDate() - 30);

        const sixtyDaysAgo = new Date();
        sixtyDaysAgo.setDate(currentDate.getDate() - 60);

        // Format dates for Supabase queries
        const thirtyDaysAgoStr = thirtyDaysAgo.toISOString();

        // Fetch current period stats
        const [
          { count: vehiclesCount },
          { count: alertsCount },
          { count: pendingAlertsCount },
          { count: historyRequestsCount },
          { count: usersCount },
        ] = await Promise.all([
          supabase.from('vehicles').select('*', { count: 'exact', head: true }),
          supabase.from('alerts').select('*', { count: 'exact', head: true }),
          supabase
            .from('alerts')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'pending'),
          supabase
            .from('history_report_requests')
            .select('*', { count: 'exact', head: true }),
          supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true }),
        ]);

        // Fetch previous period stats (30-60 days ago)
        const [
          { count: prevVehiclesCount },
          { count: prevAlertsCount },
          { count: prevPendingAlertsCount },
          { count: prevHistoryRequestsCount },
          { count: prevUsersCount },
        ] = await Promise.all([
          supabase
            .from('vehicles')
            .select('*', { count: 'exact', head: true })
            .lt('created_at', thirtyDaysAgoStr),
          supabase
            .from('alerts')
            .select('*', { count: 'exact', head: true })
            .lt('created_at', thirtyDaysAgoStr),
          supabase
            .from('alerts')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'pending')
            .lt('created_at', thirtyDaysAgoStr),
          supabase
            .from('history_report_requests')
            .select('*', { count: 'exact', head: true })
            .lt('created_at', thirtyDaysAgoStr),
          supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true })
            .lt('created_at', thirtyDaysAgoStr),
        ]);

        setStats({
          totalVehicles: vehiclesCount || 0,
          totalAlerts: alertsCount || 0,
          pendingAlerts: pendingAlertsCount || 0,
          historyRequests: historyRequestsCount || 0,
          totalUsers: usersCount || 0,
          previousTotalVehicles: prevVehiclesCount || 0,
          previousTotalAlerts: prevAlertsCount || 0,
          previousPendingAlerts: prevPendingAlertsCount || 0,
          previousHistoryRequests: prevHistoryRequestsCount || 0,
          previousTotalUsers: prevUsersCount || 0,
        });

        // Fetch recent history requests
        let requestsQuery = supabase
          .from('history_report_requests')
          .select('*, vehicles(make, model, year)')
          .order('created_at', { ascending: false })
          .limit(5);

        // If not admin or user with history_report_requests:view permission, only show user's own requests
        if (!isAdmin && !hasPermission('history_report_requests:view')) {
          requestsQuery = requestsQuery.eq('user_id', userId);
        }

        const { data: requestsData } = await requestsQuery;
        setRecentRequests(requestsData || []);

        // Fetch recent alerts
        let alertsQuery = supabase
          .from('alerts')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(5);

        // If not admin or user with alert:view permission, only show user's own alerts
        if (!isAdmin && !hasPermission('alert:view')) {
          alertsQuery = alertsQuery.eq('created_by', userId);
        }

        const { data: alertsData } = await alertsQuery;
        setRecentAlerts(alertsData || []);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchDashboardData();
  }, [userId, isAdmin, hasPermission]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DynamicTitle pageTitle="Dashboard" />
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-neutral-500">
          {greeting}, {user?.user_metadata?.full_name || 'User'}!
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
        <StatsCard
          title="Total Vehicles"
          value={stats.totalVehicles}
          previousValue={stats.previousTotalVehicles}
          icon={<Car className="h-5 w-5" />}
          trendLabel="vs. previous period"
        />

        <StatsCard
          title="Total Alerts"
          value={stats.totalAlerts}
          previousValue={stats.previousTotalAlerts}
          icon={<AlertTriangle className="h-5 w-5" />}
          trendLabel="vs. previous period"
        />

        <StatsCard
          title="Pending Alerts"
          value={stats.pendingAlerts}
          previousValue={stats.previousPendingAlerts}
          icon={<AlertTriangle className="h-5 w-5" />}
          trendLabel="vs. previous period"
        />

        <StatsCard
          title="Report Requests"
          value={stats.historyRequests}
          previousValue={stats.previousHistoryRequests}
          icon={<FileText className="h-5 w-5" />}
          trendLabel="vs. previous period"
        />

        <StatsCard
          title="Total Users"
          value={stats.totalUsers}
          previousValue={stats.previousTotalUsers}
          icon={<Users className="h-5 w-5" />}
          trendLabel="vs. previous period"
        />
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks you can perform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <Link href="/dashboard/vehicles" className="w-full">
              <Button className="w-full h-12 sm:h-10">Search Vehicle</Button>
            </Link>
            <Link href="/dashboard/alerts/new" className="w-full">
              <Button className="w-full h-12 sm:h-10">Submit Alert</Button>
            </Link>
            <Link href="/dashboard/vehicles/request" className="w-full">
              <Button className="w-full h-12 sm:h-10">Request History Record</Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle>Recent Report Requests</CardTitle>
              <CardDescription>
                Latest vehicle history report requests
              </CardDescription>
            </div>
            <Link href="/dashboard/history-reports" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              View all
            </Link>
          </CardHeader>
          <CardContent>
            {recentRequests.length > 0 ? (
              <div className="space-y-4">
                {recentRequests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">
                        {request.vehicles?.make} {request.vehicles?.model} ({request.vehicles?.year})
                      </p>
                      <p className="text-xs text-neutral-500">
                        {request.license_plate || request.vin || request.chassis_number || 'No identifier'}
                      </p>
                    </div>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        request.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : request.status === 'processing'
                          ? 'bg-yellow-100 text-yellow-800'
                          : request.status === 'error'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {request.status}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-neutral-500">No recent history requests found.</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              <CardTitle>Recent Alerts</CardTitle>
              <CardDescription>
                Latest alerts in the system
              </CardDescription>
            </div>
            <Link href="/dashboard/alerts" className="text-sm font-medium text-blue-600 hover:text-blue-500">
              View all
            </Link>
          </CardHeader>
          <CardContent>
            {recentAlerts.length > 0 ? (
              <div className="space-y-4">
                {recentAlerts.map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{alert.alert_type}</p>
                      <p className="text-xs text-neutral-500 truncate max-w-[250px]">{alert.description}</p>
                    </div>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        alert.status === 'resolved'
                          ? 'bg-green-100 text-green-800'
                          : alert.status === 'in_progress'
                          ? 'bg-yellow-100 text-yellow-800'
                          : alert.status === 'rejected'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {alert.status}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-neutral-500">No recent alerts found.</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="order-2 lg:order-1">
          <ActivityTimeline />
        </div>
        <div className="order-1 lg:order-2">
          <AlertStatusChart />
        </div>
      </div>

      {/* Vehicle Statistics */}
      <div className="mt-6">
        <VehicleStatsChart />
      </div>


    </div>
  );
}
