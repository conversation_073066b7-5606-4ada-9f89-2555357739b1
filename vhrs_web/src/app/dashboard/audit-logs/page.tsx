'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { FileDown } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

type AuditLog = {
  id: string;
  user_id: string;
  table_name: string;
  record_id: string;
  action: string;
  old_data: any;
  new_data: any;
  timestamp: string;
  user?: {
    email: string;
    full_name: string;
  };
};

export default function AuditLogs() {
  const router = useRouter();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [tableFilter, setTableFilter] = useState('all');
  const [actionFilter, setActionFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [users, setUsers] = useState<{ id: string; email: string; full_name: string | null }[]>([]);
  const [tables, setTables] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const { isAdmin, loading: permissionsLoading } = usePermissions();
  const logsPerPage = 10;

  useEffect(() => {
    if (!permissionsLoading && !isAdmin) {
      // Redirect non-admin users
      toast.error('You do not have permission to view this page');
      router.push('/dashboard');
      return;
    }

    fetchUsers();
    fetchTables();
    fetchLogs();
  }, [permissionsLoading, isAdmin, searchParams, router]);

  useEffect(() => {
    if (isAdmin) {
      fetchLogs();
    }
  }, [tableFilter, actionFilter, currentPage, searchTerm, isAdmin]);

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .order('email');

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
    }
  };

  const fetchTables = async () => {
    try {
      const { data, error } = await supabase
        .from('data_submissions')
        .select('table_name')
        .order('table_name');

      if (error) throw error;

      // Extract unique table names
      const uniqueTables = [...new Set(data.map(item => item.table_name))];
      setTables(uniqueTables);
    } catch (error) {
      console.error('Error fetching tables:', error);
      toast.error('Failed to load table information');
    }
  };

  const fetchLogs = async () => {
    setLoading(true);
    try {
      // Calculate pagination
      const from = (currentPage - 1) * logsPerPage;
      const to = from + logsPerPage - 1;

      // Start building the query
      let query = supabase
        .from('data_submissions')
        .select('*, profiles!data_submissions_user_id_fkey(email, full_name)', { count: 'exact' });

      // Apply filters
      if (tableFilter !== 'all') {
        query = query.eq('table_name', tableFilter);
      }

      if (actionFilter !== 'all') {
        query = query.eq('action', actionFilter);
      }

      // Apply search term if provided
      if (searchTerm) {
        query = query.or(`record_id.ilike.%${searchTerm}%,table_name.ilike.%${searchTerm}%`);
      }

      // Apply pagination and ordering
      const { data, error, count } = await query
        .order('timestamp', { ascending: false })
        .range(from, to);

      if (error) throw error;

      // Format the data
      const formattedLogs = data.map((log: any) => ({
        ...log,
        user: log.profiles
      }));

      setLogs(formattedLogs);

      // Calculate total pages
      if (count !== null) {
        setTotalPages(Math.ceil(count / logsPerPage));
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      toast.error('Failed to load audit logs');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on new search
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getActionBadgeClass = (action: string) => {
    switch (action) {
      case 'create':
        return 'bg-green-100 text-green-800';
      case 'update':
        return 'bg-blue-100 text-blue-800';
      case 'delete':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getUserName = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user ? (user.full_name || user.email) : userId;
  };

  const viewLogDetails = (log: AuditLog) => {
    setSelectedLog(log);
  };

  const exportToCSV = () => {
    // Create CSV content
    let csvContent = 'Timestamp,User,Action,Table,Record ID\n';

    logs.forEach(log => {
      const userName = log.user ? (log.user.full_name || log.user.email) : log.user_id;
      csvContent += `"${formatTimestamp(log.timestamp)}","${userName}","${log.action}","${log.table_name}","${log.record_id}"\n`;
    });

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `audit_logs_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Audit logs exported successfully');
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h2 className="text-2xl font-bold text-neutral-800 mb-2">Access Denied</h2>
        <p className="text-neutral-600">You do not have permission to view this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Audit Logs</h1>
        <Button onClick={exportToCSV} className="flex items-center gap-2">
          <FileDown className="h-4 w-4" />
          <span>Export to CSV</span>
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter audit logs by table, action, or search term</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="table-filter" className="mb-2 block">Table</Label>
              <Select value={tableFilter} onValueChange={setTableFilter}>
                <SelectTrigger id="table-filter">
                  <SelectValue placeholder="Select table" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tables</SelectItem>
                  {tables.map(table => (
                    <SelectItem key={table} value={table}>{table}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="action-filter" className="mb-2 block">Action</Label>
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger id="action-filter">
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Actions</SelectItem>
                  <SelectItem value="create">Create</SelectItem>
                  <SelectItem value="update">Update</SelectItem>
                  <SelectItem value="delete">Delete</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="search" className="mb-2 block">Search</Label>
              <Input
                id="search"
                placeholder="Search by record ID or table"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-16 w-full" />
          ))}
        </div>
      ) : logs.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-neutral-500">No audit logs found matching your criteria.</p>
        </div>
      ) : (
        <>
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Table</TableHead>
                  <TableHead>Record ID</TableHead>
                  <TableHead className="text-right">Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.map(log => (
                  <TableRow key={log.id}>
                    <TableCell className="font-mono text-xs">
                      {formatTimestamp(log.timestamp)}
                    </TableCell>
                    <TableCell>
                      {log.user ? (log.user.full_name || log.user.email) : log.user_id}
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getActionBadgeClass(log.action)}`}>
                        {log.action}
                      </span>
                    </TableCell>
                    <TableCell>{log.table_name}</TableCell>
                    <TableCell className="font-mono text-xs">{log.record_id}</TableCell>
                    <TableCell className="text-right">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => viewLogDetails(log)}>
                            View
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl">
                          <DialogHeader>
                            <DialogTitle>Audit Log Details</DialogTitle>
                            <DialogDescription>
                              {formatTimestamp(log.timestamp)} - {log.action} on {log.table_name}
                            </DialogDescription>
                            <div className="mt-1 text-sm font-medium text-muted-foreground">
                              By: {log.user ? (log.user.full_name || log.user.email) : log.user_id}
                            </div>
                          </DialogHeader>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                              <h3 className="font-semibold mb-2">Previous Data</h3>
                              <pre className="bg-neutral-100 p-4 rounded-md overflow-auto max-h-96 text-xs">
                                {log.old_data ? JSON.stringify(log.old_data, null, 2) : 'No previous data'}
                              </pre>
                            </div>
                            <div>
                              <h3 className="font-semibold mb-2">New Data</h3>
                              <pre className="bg-neutral-100 p-4 rounded-md overflow-auto max-h-96 text-xs">
                                {log.new_data ? JSON.stringify(log.new_data, null, 2) : 'No new data'}
                              </pre>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <Pagination className="mt-6">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    size="default"
                    onClick={() => handlePageChange(currentPage - 1)}
                    className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter(page => {
                    // Show first page, last page, current page, and pages around current page
                    return page === 1 ||
                           page === totalPages ||
                           (page >= currentPage - 1 && page <= currentPage + 1);
                  })
                  .map((page, i, array) => {
                    // Add ellipsis if there are gaps
                    const prevPage = array[i - 1];
                    const showEllipsisBefore = prevPage && page - prevPage > 1;

                    return (
                      <React.Fragment key={page}>
                        {showEllipsisBefore && (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}
                        <PaginationItem>
                          <PaginationLink
                            size="default"
                            onClick={() => handlePageChange(page)}
                            isActive={page === currentPage}
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      </React.Fragment>
                    );
                  })}

                <PaginationItem>
                  <PaginationNext
                    size="default"
                    onClick={() => handlePageChange(currentPage + 1)}
                    className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </>
      )}
    </div>
  );
}
