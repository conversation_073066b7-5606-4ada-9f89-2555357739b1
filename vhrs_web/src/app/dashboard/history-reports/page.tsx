'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';
import { Check } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface HistoryRequest {
  id: string;
  user_id: string;
  vehicle_id: string;
  license_plate: string | null;
  vin: string | null;
  request_method: string;
  status: string;
  document_url: string | null;
  viewed_in_app: boolean;
  created_at: string;
  completed_at: string | null;
  vehicles: {
    make: string;
    model: string;
    year: number;
  } | null;
  profiles: {
    full_name: string | null;
    email: string;
  } | null;
}



export default function HistoryReportsPage() {
  const { userId, isAdmin, hasPermission, loading: permissionsLoading } = usePermissions();
  const [requests, setRequests] = useState<HistoryRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [userIdFilter, setUserIdFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showMyRequestsOnly, setShowMyRequestsOnly] = useState(false);

  // Fetch history requests
  useEffect(() => {
    if (permissionsLoading) return;
    fetchHistoryRequests();
  }, [permissionsLoading, isAdmin, userId]);

  const fetchHistoryRequests = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('history_report_requests')
        .select('*, vehicles(make, model, year), profiles(full_name, email)')
        .order('created_at', { ascending: false });

      // If not admin or user with history_report_requests:view permission, only show user's own requests
      if (!isAdmin && !hasPermission('history_report_requests:view')) {
        query = query.eq('user_id', userId);
      } else if (showMyRequestsOnly) {
        query = query.eq('user_id', userId);
      } else if (userIdFilter) {
        query = query.eq('user_id', userIdFilter);
      }

      // Apply status filter if not 'all'
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      const { data, error } = await query;

      if (error) throw error;
      setRequests(data || []);
    } catch (error) {
      console.error('Error fetching history requests:', error);
      toast.error('Failed to load history requests');
    } finally {
      setLoading(false);
    }
  };

  const handleUserIdSearch = () => {
    // Reset the "My Requests" filter
    setShowMyRequestsOnly(false);

    // Immediately fetch data with the user ID filter
    setLoading(true);

    let query = supabase
      .from('history_report_requests')
      .select('*, vehicles(make, model, year), profiles(full_name, email)')
      .order('created_at', { ascending: false });

    // Apply the user ID filter if provided
    if (userIdFilter) {
      query = query.eq('user_id', userIdFilter);
    }

    // Apply status filter if not 'all'
    if (statusFilter !== 'all') {
      query = query.eq('status', statusFilter);
    }

    query.then(({ data, error }) => {
      if (error) {
        console.error('Error fetching history requests:', error);
        toast.error('Failed to load history requests');
      } else {
        setRequests(data || []);
      }
      setLoading(false);
    });
  };

  const handleStatusFilterChange = (value: string) => {
    // Set the new status filter value
    setStatusFilter(value);

    // Immediately fetch data with the new filter
    setLoading(true);

    let query = supabase
      .from('history_report_requests')
      .select('*, vehicles(make, model, year), profiles(full_name, email)')
      .order('created_at', { ascending: false });

    // Apply user filters
    if (!isAdmin && !hasPermission('history_report_requests:view')) {
      query = query.eq('user_id', userId);
    } else if (showMyRequestsOnly) {
      query = query.eq('user_id', userId);
    } else if (userIdFilter) {
      query = query.eq('user_id', userIdFilter);
    }

    // Apply the new status filter immediately
    if (value !== 'all') {
      query = query.eq('status', value);
    }

    query.then(({ data, error }) => {
      if (error) {
        console.error('Error fetching history requests:', error);
        toast.error('Failed to load history requests');
      } else {
        setRequests(data || []);
      }
      setLoading(false);
    });
  };

  const handleToggleMyRequests = () => {
    // Toggle the state first
    const newValue = !showMyRequestsOnly;
    setShowMyRequestsOnly(newValue);
    setUserIdFilter('');

    // Then fetch the data with the new filter
    setLoading(true);

    let query = supabase
      .from('history_report_requests')
      .select('*, vehicles(make, model, year), profiles(full_name, email)')
      .order('created_at', { ascending: false });

    // Apply the new filter value immediately
    if (newValue) {
      query = query.eq('user_id', userId);
    }

    if (statusFilter !== 'all') {
      query = query.eq('status', statusFilter);
    }

    query.then(({ data, error }) => {
      if (error) {
        console.error('Error fetching history requests:', error);
        toast.error('Failed to load history requests');
      } else {
        setRequests(data || []);
      }
      setLoading(false);
    });
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Filter requests by search term
  const filteredRequests = requests.filter((request) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      (request.vehicles?.make && request.vehicles.make.toLowerCase().includes(searchLower)) ||
      (request.vehicles?.model && request.vehicles.model.toLowerCase().includes(searchLower)) ||
      (request.license_plate && request.license_plate.toLowerCase().includes(searchLower)) ||
      (request.vin && request.vin.toLowerCase().includes(searchLower)) ||
      (request.profiles?.full_name && request.profiles.full_name.toLowerCase().includes(searchLower)) ||
      (request.profiles?.email && request.profiles.email.toLowerCase().includes(searchLower))
    );
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>;
      case 'processing':
        return <Badge variant="secondary">Processing</Badge>;
      case 'generated':
        return <Badge variant="default">Generated</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">History Report Requests</h1>
          <p className="text-neutral-500">
            {isAdmin || hasPermission('history_report_requests:view')
              ? 'View and manage all vehicle history report requests'
              : 'View your vehicle history report requests'}
          </p>
        </div>
        <div className="flex gap-2">

          <Link href="/dashboard/vehicles/request">
            <Button>Request New Report</Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>History Reports</CardTitle>
          <CardDescription>
            {isAdmin || hasPermission('history_report_requests:view')
              ? 'All vehicle history report requests in the system'
              : 'Your vehicle history report requests'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search by vehicle, VIN, plate, or user..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-full"
              />
            </div>

            <div className="w-full md:w-48">
              <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="generated">Generated</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {(isAdmin || hasPermission('history_report_requests:view')) && (
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 md:flex-initial md:w-64">
                <Input
                  placeholder="Enter User ID"
                  value={userIdFilter}
                  onChange={(e) => setUserIdFilter(e.target.value)}
                />
              </div>
              <Button
                variant="secondary"
                onClick={handleUserIdSearch}
              >
                Filter by User ID
              </Button>
              <Button
                variant={showMyRequestsOnly ? "default" : "outline"}
                onClick={handleToggleMyRequests}
                className="flex items-center gap-2"
              >
                {showMyRequestsOnly && <Check className="h-4 w-4" />}
                My Requests
              </Button>
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredRequests.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Identifier</TableHead>
                    {(isAdmin || hasPermission('history_report_requests:view')) && <TableHead>Requested By</TableHead>}
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell className="font-medium">
                        {request.vehicles ? (
                          `${request.vehicles.make} ${request.vehicles.model} (${request.vehicles.year})`
                        ) : (
                          'Unknown Vehicle'
                        )}
                      </TableCell>
                      <TableCell>
                        {request.license_plate ? (
                          <span className="font-mono">LP: {request.license_plate}</span>
                        ) : request.vin ? (
                          <span className="font-mono text-xs">VIN: {request.vin}</span>
                        ) : (
                          'N/A'
                        )}
                      </TableCell>
                      {(isAdmin || hasPermission('history_report_requests:view')) && (
                        <TableCell>
                          {request.profiles ? (
                            request.profiles.full_name || request.profiles.email
                          ) : (
                            'Unknown User'
                          )}
                        </TableCell>
                      )}
                      <TableCell>{formatDate(request.created_at)}</TableCell>
                      <TableCell>{getStatusBadge(request.status)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          {request.status === 'generated' && (
                            <Link href={`/dashboard/vehicles/${request.vehicle_id}/history`}>
                              <Button size="sm">View Report</Button>
                            </Link>
                          )}
                          <Link href={`/dashboard/vehicles/${request.vehicle_id}`}>
                            <Button variant="outline" size="sm">View Vehicle</Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-neutral-500">No history requests found.</p>
              <p className="mt-2">
                {isAdmin || hasPermission('history_report_requests:view')
                  ? 'Try adjusting your filters or search criteria.'
                  : 'You have not requested any vehicle history reports yet.'}
              </p>
              <div className="mt-4">
                <Link href="/dashboard/vehicles/request">
                  <Button>Request Vehicle Report</Button>
                </Link>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
