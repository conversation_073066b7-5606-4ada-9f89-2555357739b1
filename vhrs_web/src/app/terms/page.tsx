'use client';

import { LandingFooter, LandingHeader } from '@/components/landing-page';
import { Card, CardContent } from '@/components/ui/card';
import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

export default function TermsOfServicePage() {
  const { settings } = useSystemSettings();
  const [currentDate, setCurrentDate] = useState('');

  useEffect(() => {
    setCurrentDate(new Date().toLocaleDateString());
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <LandingHeader />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary/5 to-primary/10 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Terms of Service
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Please read these terms carefully before using our vehicle history record services.
              </p>
              <p className="text-sm text-gray-500 mt-4">
                Last updated: {currentDate || 'Loading...'}
              </p>
            </motion.div>
          </div>
        </section>

        {/* Terms Content */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card>
                <CardContent className="p-8 space-y-8">

                  <TermsSection
                    title="1. Acceptance of Terms"
                    content={[
                      "By accessing and using our vehicle history record services, you accept and agree to be bound by the terms and provision of this agreement.",
                      "If you do not agree to abide by the above, please do not use this service.",
                      "These terms apply to all users of the service, including without limitation users who are browsers, vendors, customers, merchants, and/or contributors of content."
                    ]}
                  />

                  <TermsSection
                    title="2. Service Description"
                    content={[
                      "Our service provides vehicle history reports and related information based on vehicle identification numbers (VINs), license plates, and other vehicle identifiers.",
                      "We aggregate data from various sources to provide comprehensive vehicle history information.",
                      "The service is intended for informational purposes and to assist in making informed decisions about vehicle purchases and ownership."
                    ]}
                  />

                  <TermsSection
                    title="3. User Accounts and Registration"
                    content={[
                      "To access certain features of our service, you must register for an account and provide accurate, current, and complete information.",
                      "You are responsible for maintaining the confidentiality of your account credentials.",
                      "You agree to notify us immediately of any unauthorized use of your account.",
                      "You must be at least 18 years old to create an account and use our services."
                    ]}
                  />

                  <TermsSection
                    title="4. Acceptable Use"
                    content={[
                      "You agree to use our services only for lawful purposes and in accordance with these terms.",
                      "You will not use the service to violate any applicable laws or regulations.",
                      "You will not attempt to gain unauthorized access to our systems or other users' accounts.",
                      "You will not use automated systems or software to extract data from our service without permission.",
                      "You will not share your account credentials with others or allow others to use your account."
                    ]}
                  />

                  <TermsSection
                    title="5. Data Accuracy and Limitations"
                    content={[
                      "While we strive to provide accurate and up-to-date information, we cannot guarantee the completeness or accuracy of all data.",
                      "Vehicle history reports are based on available data sources and may not include all incidents or events.",
                      "You should verify important information through additional sources before making significant decisions.",
                      "We are not responsible for decisions made based solely on our reports."
                    ]}
                  />

                  <TermsSection
                    title="6. Payment and Billing"
                    content={[
                      "Certain services may require payment of fees as described on our website.",
                      "All fees are non-refundable unless otherwise stated or required by law.",
                      "We reserve the right to change our pricing at any time with appropriate notice.",
                      "You are responsible for all charges incurred under your account."
                    ]}
                  />

                  <TermsSection
                    title="7. Intellectual Property"
                    content={[
                      "The service and its original content, features, and functionality are owned by us and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.",
                      "You may not reproduce, distribute, modify, create derivative works of, publicly display, publicly perform, republish, download, store, or transmit any of the material on our service without prior written consent."
                    ]}
                  />

                  <TermsSection
                    title="8. Privacy"
                    content={[
                      "Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the service.",
                      "By using our service, you consent to the collection and use of information as described in our Privacy Policy."
                    ]}
                  />

                  <TermsSection
                    title="9. Disclaimers and Limitation of Liability"
                    content={[
                      "The service is provided on an 'as is' and 'as available' basis without any warranties of any kind.",
                      "We disclaim all warranties, whether express or implied, including but not limited to implied warranties of merchantability, fitness for a particular purpose, and non-infringement.",
                      "In no event shall we be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses."
                    ]}
                  />

                  <TermsSection
                    title="10. Indemnification"
                    content={[
                      "You agree to defend, indemnify, and hold harmless the company and its licensee and licensors, and their employees, contractors, agents, officers and directors, from and against any and all claims, damages, obligations, losses, liabilities, costs or debt, and expenses (including but not limited to attorney's fees)."
                    ]}
                  />

                  <TermsSection
                    title="11. Termination"
                    content={[
                      "We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation.",
                      "If you wish to terminate your account, you may simply discontinue using the service.",
                      "Upon termination, your right to use the service will cease immediately."
                    ]}
                  />

                  <TermsSection
                    title="12. Governing Law"
                    content={[
                      "These terms shall be interpreted and governed by the laws of Uganda.",
                      "Any disputes arising from these terms or your use of the service shall be subject to the jurisdiction of the courts of Uganda."
                    ]}
                  />

                  <TermsSection
                    title="13. Changes to Terms"
                    content={[
                      "We reserve the right, at our sole discretion, to modify or replace these terms at any time.",
                      "If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.",
                      "Your continued use of the service after any changes constitutes acceptance of the new terms."
                    ]}
                  />

                  <TermsSection
                    title="14. Contact Information"
                    content={[
                      "If you have any questions about these Terms of Service, please contact us:",
                      settings.contact_email ? `Email: ${settings.contact_email}` : '',
                      settings.support_phone ? `Phone: ${settings.support_phone}` : '',
                      "Address: Kampala, Uganda"
                    ].filter(Boolean)}
                  />

                </CardContent>
              </Card>
            </motion.div>
          </div>
        </section>
      </main>

      <LandingFooter />
    </div>
  );
}

interface TermsSectionProps {
  title: string;
  content: string[];
}

function TermsSection({ title, content }: TermsSectionProps) {
  return (
    <div>
      <h2 className="text-xl font-bold text-gray-900 mb-4">{title}</h2>
      <div className="space-y-3">
        {content.map((paragraph, index) => (
          <p key={index} className="text-gray-600 leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    </div>
  );
}
