import { createClient } from '@supabase/supabase-js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  // Check for auth cookie directly
  const hasAuthCookie = request.cookies.has('sb-access-token') ||
                        request.cookies.has('sb-refresh-token') ||
                        request.cookies.has('supabase-auth-token');

  // Create a Supabase client with cookie support
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL as string,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        storage: {
          getItem: (key) => {
            const cookies = request.cookies.getAll();
            const cookie = cookies.find((c) => c.name === key);
            return cookie?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
    }
  );

  try {
    // Get the session from Supabase
    const { data: { session } } = await supabase.auth.getSession();

    const isAuthenticated = !!session || hasAuthCookie;

    // If not authenticated and trying to access protected route
    if (!isAuthenticated && request.nextUrl.pathname.startsWith('/dashboard')) {
      // Redirect to the login page
      const redirectUrl = new URL('/auth/signin', request.url);
      return NextResponse.redirect(redirectUrl);
    }

    // If authenticated and trying to access auth pages
    if (isAuthenticated && (
      request.nextUrl.pathname.startsWith('/auth/signin') ||
      request.nextUrl.pathname.startsWith('/auth/signup')
    )) {
      // Redirect to the dashboard
      const redirectUrl = new URL('/dashboard', request.url);
      return NextResponse.redirect(redirectUrl);
    }
  } catch (error) {
    console.error('Middleware error:', error);
  }

  return NextResponse.next();
}

// Specify which routes this middleware should run on
export const config = {
  matcher: ['/dashboard/:path*', '/auth/:path*'],
};
