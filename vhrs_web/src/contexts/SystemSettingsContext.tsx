'use client';

import { supabase } from '@/lib/supabase';
import { createContext, useContext, useEffect, useState } from 'react';

// Define the shape of our system settings
export interface PublicSystemSettings {
  system_name: string;
  contact_email: string;
  support_phone: string | null;
  maintenance_mode: boolean;
  maintenance_message: string | null;
  allow_public_registration: boolean;
  alert_categories: string;
}

// Default values for system settings
const defaultSettings: PublicSystemSettings = {
  system_name: 'Vehicle History Record System',
  contact_email: '<EMAIL>',
  support_phone: null,
  maintenance_mode: false,
  maintenance_message: null,
  allow_public_registration: true,
  alert_categories: 'accident,fraud,theft,damage,other',
};

interface SystemSettingsContextType {
  settings: PublicSystemSettings;
  loading: boolean;
  error: Error | null;
  refreshSettings: () => Promise<void>;
}

// Create the context with default values
const SystemSettingsContext = createContext<SystemSettingsContextType>({
  settings: defaultSettings,
  loading: true,
  error: null,
  refreshSettings: async () => {},
});

// Custom hook to use the system settings
export const useSystemSettings = () => useContext(SystemSettingsContext);

export const SystemSettingsProvider = ({ children }: { children: React.ReactNode }) => {
  const [settings, setSettings] = useState<PublicSystemSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Function to fetch system settings
  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Call the RPC function to get public system settings
      const { data, error } = await supabase.rpc('get_public_system_settings');

      if (error) {
        throw new Error(`Failed to fetch system settings: ${error.message}`);
      }

      // If we got data, update the settings
      if (data) {
        setSettings({
          ...defaultSettings, // Fallback for any missing fields
          ...data,
        });
      }
    } catch (err) {
      console.error('Error fetching system settings:', err);
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
    } finally {
      setLoading(false);
    }
  };

  // Fetch settings on initial load
  useEffect(() => {
    fetchSettings();
  }, []);

  // Provide a way to manually refresh settings
  const refreshSettings = async () => {
    await fetchSettings();
  };

  return (
    <SystemSettingsContext.Provider
      value={{
        settings,
        loading,
        error,
        refreshSettings,
      }}
    >
      {children}
    </SystemSettingsContext.Provider>
  );
};
