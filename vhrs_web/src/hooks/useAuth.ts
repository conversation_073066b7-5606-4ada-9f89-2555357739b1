import { supabase } from '@/lib/supabase';
import { Session, User } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get the current session
    const getSession = async () => {
      setLoading(true);
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error getting session:', error);
      } else {
        setSession(session);
        setUser(session?.user ?? null);
      }

      setLoading(false);
    };

    getSession();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Function to sign up a new user
  const signUp = async (email: string, password: string, fullName: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });

    return { data, error };
  };

  // Function to sign in a user
  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (!error && data.session) {
      // Update local state immediately
      setSession(data.session);
      setUser(data.user);

      // Manually set cookies for middleware access
      document.cookie = `sb-access-token=${data.session.access_token}; path=/; max-age=3600; SameSite=Lax`;
      document.cookie = `sb-refresh-token=${data.session.refresh_token}; path=/; max-age=7776000; SameSite=Lax`;

      // Store auth data in localStorage as a backup
      localStorage.setItem('supabase-auth-token', JSON.stringify([data.session.access_token, data.session.refresh_token]));
    }

    return { data, error };
  };

  // Function to sign out
  const signOut = async () => {
    const { error } = await supabase.auth.signOut();

    // Manually clear cookies
    document.cookie = 'sb-access-token=; path=/; max-age=0; SameSite=Lax';
    document.cookie = 'sb-refresh-token=; path=/; max-age=0; SameSite=Lax';
    document.cookie = 'supabase-auth-token=; path=/; max-age=0; SameSite=Lax';

    // Clear localStorage
    localStorage.removeItem('supabase-auth-token');

    // Reset state
    setUser(null);
    setSession(null);

    return { error };
  };

  // Function to reset password
  const resetPassword = async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/update-password`,
    });

    return { data, error };
  };

  // Function to update password
  const updatePassword = async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password,
    });

    return { data, error };
  };

  // Function to update user profile
  const updateProfile = async (fullName: string) => {
    // First update the auth user metadata
    const { data, error } = await supabase.auth.updateUser({
      data: {
        full_name: fullName,
      },
    });

    if (error) {
      return { data, error };
    }

    // Then update the profiles table
    if (user) {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ full_name: fullName })
        .eq('id', user.id);

      if (profileError) {
        console.error('Error updating profile in database:', profileError);
        return {
          data,
          error: {
            message: 'Profile updated in auth system but failed to update in database. Please try again.'
          }
        };
      }
    }

    return { data, error };
  };

  return {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
  };
}
