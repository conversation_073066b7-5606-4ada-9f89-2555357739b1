import { supabase } from '@/lib/supabase';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from './useAuth';

/**
 * Hook for accessing user roles
 * Note: For permission checks, use usePermissions instead
 */
export function useUserRoles() {
  const { user } = useAuth();
  const [roles, setRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const isMounted = useRef(true);

  // Cache TTL in milliseconds (5 minutes)
  const CACHE_TTL = 5 * 60 * 1000;
  const lastFetched = useRef<number>(0);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  const fetchRoles = useCallback(async (force = false) => {
    if (!user) {
      setRoles([]);
      setLoading(false);
      return;
    }

    // Skip if roles were recently fetched (unless forced)
    const now = Date.now();
    if (!force && lastFetched.current > 0 && now - lastFetched.current < CACHE_TTL) {
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_user_roles', {
        user_id: user.id
      });

      if (isMounted.current) {
        if (error) {
          console.error('Error fetching user roles:', error);
          // Don't clear roles on error to maintain last known state
        } else {
          setRoles(data || []);
          lastFetched.current = now;
        }
        setLoading(false);
      }
    } catch (error) {
      if (isMounted.current) {
        console.error('Error in fetchRoles:', error);
        setLoading(false);
      }
    }
  }, [user, CACHE_TTL]);

  useEffect(() => {
    fetchRoles();
  }, [fetchRoles]);

  // Check if user has a specific role
  const hasRole = useCallback((roleName: string): boolean => {
    return roles.includes(roleName);
  }, [roles]);

  return {
    roles,
    loading,
    hasRole,
    refreshRoles: () => fetchRoles(true)
  };
}
