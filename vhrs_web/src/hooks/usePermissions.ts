'use client';

import { useAuth } from '@/hooks/useAuth';
import { useUserRoles } from '@/hooks/useUserRoles';
import { supabase } from '@/lib/supabase';
import { useCallback, useEffect, useState } from 'react';

// Define permission types
type Permission = string;
type PermissionSet = Record<Permission, boolean>;

// Cache TTL in milliseconds (5 minutes)
const PERMISSIONS_CACHE_TTL = 5 * 60 * 1000;

export function usePermissions() {
  const { user, loading: authLoading } = useAuth();
  const { roles, loading: rolesLoading } = useUserRoles();
  const [permissions, setPermissions] = useState<PermissionSet>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetched, setLastFetched] = useState<number>(0);

  // Use actual roles instead of deriving them from permissions
  const isAdmin = roles.includes('admin');
  // Get primary role - admin takes precedence, otherwise use the first role or default to 'user'
  const userRole = isAdmin ? 'admin' : (roles.length > 0 ? roles[0] : 'user');

  // Fetch all permissions for the current user
  const fetchPermissions = useCallback(async (force = false) => {
    if (!user) {
      setPermissions({});
      setLoading(false);
      return;
    }

    // Skip if permissions were recently fetched (unless forced)
    const now = Date.now();
    if (!force && lastFetched > 0 && now - lastFetched < PERMISSIONS_CACHE_TTL) {
      return;
    }

    setLoading(true);
    try {
      // Call the Supabase RPC function to get all permissions
      const { data, error } = await supabase.rpc('get_user_permissions');

      if (error) throw new Error(error.message || 'Failed to fetch permissions');

      setPermissions(data?.permissions || {});
      setLastFetched(now);
      setError(null);
    } catch (err) {
      console.error('Error fetching permissions:', err);
      setError(err instanceof Error ? err : new Error('Unknown error'));
      // Don't clear permissions on error to maintain last known state
    } finally {
      setLoading(false);
    }
  }, [user, lastFetched]);

  // Fetch permissions on mount and when user changes
  useEffect(() => {
    if (!authLoading) {
      fetchPermissions();
    }
  }, [authLoading, fetchPermissions]);

  // Check if user has a specific permission
  const hasPermission = useCallback((permission: Permission): boolean => {
    // If we have an explicit permission value, use it
    if (permissions.hasOwnProperty(permission)) {
      return permissions[permission] === true;
    }

    // For backward compatibility, handle permissions not explicitly returned since Admins have all permissions
    if (isAdmin) return true;

    // Default deny for unknown permissions
    return false;
  }, [permissions, isAdmin]);

  return {
    permissions,
    hasPermission,
    loading: loading || rolesLoading, // Consider both roles and normal loading states
    error,
    isAdmin,
    userRole,
    roles, // Return all roles for more flexible role-based checks
    userId: user?.id || null,
    refreshPermissions: () => fetchPermissions(true)
  };
}
