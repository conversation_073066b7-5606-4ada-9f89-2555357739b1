'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { supabase } from '@/lib/supabase';
import { FileDown, Printer, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface VehicleHistoryReportProps {
  vehicleId?: string;
  historyData?: any;
  isLoading?: boolean;
  history?: any;
}

export function EnhancedVehicleHistoryReport({
  vehicleId,
  historyData,
  isLoading = false,
  history: initialHistory,
}: VehicleHistoryReportProps) {
  const [history, setHistory] = useState<any>(initialHistory || null);
  const [loading, setLoading] = useState(isLoading);
  const [isPrinting, setIsPrinting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (historyData) {
      setHistory(historyData);
      setLoading(false);
    }
  }, [historyData]);

  const handlePrint = () => {
    setIsPrinting(true);
    setTimeout(() => {
      window.print();
      setIsPrinting(false);
    }, 100);
  };

  const handleExport = () => {
    if (!history) return;

    // Create a JSON blob and download it
    const dataStr = JSON.stringify(history, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `vehicle_history_${history.vin || 'unknown'}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Vehicle history exported successfully');
  };

  const handleSaveToHistory = async () => {
    if (!history || !vehicleId) return;

    setIsSaving(true);
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Save the history record to the database
      const { error } = await supabase
        .from('history_report_requests')
        .insert({
          user_id: user.id,
          vehicle_id: vehicleId,
          request_method: 'manual',
          status: 'generated',
          history_report: history,
          viewed_in_app: true,
          completed_at: new Date().toISOString(),
        });

      if (error) {
        throw error;
      }

      toast.success('Vehicle history saved successfully');
    } catch (error) {
      console.error('Error saving vehicle history:', error);
      toast.error('Failed to save vehicle history');
    } finally {
      setIsSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'minor':
        return <Badge variant="outline">Minor</Badge>;
      case 'major':
        return <Badge variant="secondary">Major</Badge>;
      case 'totaled':
        return <Badge variant="destructive">Totaled</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle History Record</CardTitle>
          <CardDescription>Loading vehicle history...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-6">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!history) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle History Record</CardTitle>
          <CardDescription>No history record available</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-neutral-500">
            No history record is available for this vehicle. You can request a history record to view detailed information.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 print:m-0 print:p-0 print:shadow-none">
      <div className="flex justify-between items-center print:hidden">
        <h2 className="text-xl font-bold">Vehicle History Report</h2>
        <div className="flex gap-2">
          <Button onClick={handlePrint} disabled={isPrinting} variant="outline" className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            <span>{isPrinting ? 'Preparing...' : 'Print Report'}</span>
          </Button>
          <Button onClick={handleExport} variant="outline" className="flex items-center gap-2">
            <FileDown className="h-4 w-4" />
            <span>Export JSON</span>
          </Button>
          {vehicleId && (
            <Button onClick={handleSaveToHistory} disabled={isSaving} variant="default" className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              <span>{isSaving ? 'Saving...' : 'Save to History'}</span>
            </Button>
          )}
        </div>
      </div>

      <Card className="print:shadow-none print:border-none">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl">
                {history.make} {history.model} ({history.year})
              </CardTitle>
              <CardDescription>
                VIN: {history.vin} • Type: {history.type} • Generated: {formatDate(history.generated_at)}
              </CardDescription>
            </div>
            <div className="print:hidden">
              <Badge variant="outline" className="text-sm">
                Official Record
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Ownership History Section */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Ownership History</h3>
            {history.ownership && history.ownership.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Owner</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>From</TableHead>
                    <TableHead>To</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.ownership.map((record: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{record.owner_name}</TableCell>
                      <TableCell>{record.owner_phone}</TableCell>
                      <TableCell>{formatDate(record.start_date)}</TableCell>
                      <TableCell>{record.end_date ? formatDate(record.end_date) : 'Present'}</TableCell>
                      <TableCell>
                        {record.is_current ? (
                          <Badge variant="default">Current</Badge>
                        ) : (
                          <Badge variant="outline">Previous</Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-neutral-500 italic">No ownership records available</p>
            )}
          </div>

          {/* Maintenance History Section */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Maintenance History</h3>
            {history.maintenance && history.maintenance.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service Type</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Mileage</TableHead>
                    <TableHead>Garage</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.maintenance.map((record: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{record.service_type}</TableCell>
                      <TableCell>{formatDate(record.date)}</TableCell>
                      <TableCell>{record.mileage ? `${record.mileage} km` : 'Not recorded'}</TableCell>
                      <TableCell>{record.garage_name || 'Not specified'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-neutral-500 italic">No maintenance records available</p>
            )}
          </div>

          {/* Accident History Section */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Accident History</h3>
            {history.accidents && history.accidents.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.accidents.map((record: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell>{formatDate(record.date)}</TableCell>
                      <TableCell>
                        {getSeverityBadge(record.severity)}
                      </TableCell>
                      <TableCell>
                        {record.photos && record.photos.length > 0
                          ? `${record.photos.length} photo(s) available`
                          : 'No additional details'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-neutral-500 italic">No accident records available</p>
            )}
          </div>

          <div className="border-t pt-4">
            <p className="text-sm text-neutral-500">
              This vehicle history report was generated on {formatDate(history.generated_at)} by the Vehicle History Record System.
              This document serves as an official report of the vehicle's history as recorded in the system.
            </p>
          </div>
        </CardContent>
      </Card>

      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .print\\:shadow-none,
          .print\\:shadow-none * {
            visibility: visible;
          }
          .print\\:shadow-none {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
}
