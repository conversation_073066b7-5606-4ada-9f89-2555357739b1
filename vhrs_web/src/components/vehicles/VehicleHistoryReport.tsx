'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect, useState } from 'react';

interface VehicleHistoryReportProps {
  vehicleId?: string;
  historyData?: any;
  isLoading?: boolean;
  history?: any;
}

export function VehicleHistoryReport({
  vehicleId,
  historyData,
  isLoading = false,
  history: initialHistory,
}: VehicleHistoryReportProps) {
  const [history, setHistory] = useState<any>(initialHistory || null);
  const [loading, setLoading] = useState(isLoading);
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    if (historyData) {
      setHistory(historyData);
      setLoading(false);
    }
  }, [historyData]);

  const handlePrint = () => {
    setIsPrinting(true);
    setTimeout(() => {
      window.print();
      setIsPrinting(false);
    }, 100);
  };

  const handleExport = () => {
    if (!history) return;

    // Create a JSON blob and download it
    const dataStr = JSON.stringify(history, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `vehicle_history_${history.vin || 'unknown'}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle History Record</CardTitle>
          <CardDescription>Loading vehicle history...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-6">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!history) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle History Record</CardTitle>
          <CardDescription>No history record available</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-neutral-500">
            No history record is available for this vehicle. You can request a history record to view detailed information.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 print:m-0 print:p-0 print:shadow-none">
      <div className="flex justify-between items-center print:hidden">
        <h2 className="text-xl font-bold">Vehicle History Report</h2>
        <div className="flex gap-2">
          <Button onClick={handlePrint} disabled={isPrinting} variant="outline">
            {isPrinting ? 'Preparing...' : 'Print Report'}
          </Button>
          <Button onClick={handleExport} variant="outline">
            Export JSON
          </Button>
        </div>
      </div>

      <Card className="print:shadow-none print:border-none">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Vehicle History Report</CardTitle>
              <CardDescription>
                Complete history for {history.make} {history.model} ({history.year})
              </CardDescription>
            </div>
            <div className="print:hidden">
              <Badge variant="outline" className="text-sm">
                Official Report
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Vehicle Information */}
          <div>
            <h3 className="text-lg font-medium mb-2">Vehicle Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-neutral-500">VIN</p>
                <p>{history.vin || 'Not available'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-neutral-500">Make & Model</p>
                <p>{history.make} {history.model}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-neutral-500">Year</p>
                <p>{history.year}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-neutral-500">Type</p>
                <p>{history.type || 'Not specified'}</p>
              </div>
            </div>
          </div>

          {/* Ownership History */}
          <div>
            <h3 className="text-lg font-medium mb-2">Ownership History</h3>
            {history.ownership && history.ownership.length > 0 ? (
              <div className="space-y-4">
                {history.ownership.map((owner: any, index: number) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-neutral-500">Owner</p>
                        <p>{owner.owner_name || 'Not disclosed'}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-neutral-500">Period</p>
                        <p>
                          {new Date(owner.start_date).toLocaleDateString()} -
                          {owner.end_date ? new Date(owner.end_date).toLocaleDateString() : 'Present'}
                        </p>
                      </div>
                      {owner.location && (
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-neutral-500">Location</p>
                          <p>{owner.location}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-neutral-500">No ownership history available</p>
            )}
          </div>

          {/* Accident History */}
          <div>
            <h3 className="text-lg font-medium mb-2">Accident History</h3>
            {history.accidents && history.accidents.length > 0 ? (
              <div className="space-y-4">
                {history.accidents.map((accident: any, index: number) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-neutral-500">Date</p>
                        <p>{new Date(accident.accident_date || accident.date).toLocaleDateString()}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-neutral-500">Severity</p>
                        <p>{accident.severity || 'Not specified'}</p>
                      </div>
                      <div className="space-y-1 md:col-span-2">
                        <p className="text-sm font-medium text-neutral-500">Description</p>
                        <p>{accident.description || 'No description available'}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-neutral-500">No accident history available</p>
            )}
          </div>

          {/* Maintenance Records */}
          <div>
            <h3 className="text-lg font-medium mb-2">Maintenance Records</h3>
            {history.maintenance && history.maintenance.length > 0 ? (
              <div className="space-y-4">
                {history.maintenance.map((record: any, index: number) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-neutral-500">Date</p>
                        <p>{new Date(record.service_date || record.date).toLocaleDateString()}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-neutral-500">Type</p>
                        <p>{record.service_type || 'Not specified'}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-neutral-500">Mileage</p>
                        <p>{record.mileage ? `${record.mileage} km` : 'Not recorded'}</p>
                      </div>
                      <div className="space-y-1 md:col-span-2">
                        <p className="text-sm font-medium text-neutral-500">Description</p>
                        <p>{record.description || 'No description available'}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-neutral-500">No maintenance records available</p>
            )}
          </div>

          <div className="border-t pt-4 text-xs text-neutral-400 text-right">
            Generated: {history.generated_at ? new Date(history.generated_at).toLocaleString() : new Date().toLocaleString()}
          </div>
        </CardContent>
      </Card>

      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .print\\:shadow-none,
          .print\\:shadow-none * {
            visibility: visible;
          }
          .print\\:shadow-none {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
}
