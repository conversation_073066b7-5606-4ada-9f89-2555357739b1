'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatDate } from '@/lib/utils';
import { Download, FileDown, Printer } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface PrintableVehicleHistoryReportProps {
  vehicleId?: string;
  historyData?: any;
  isLoading?: boolean;
  history?: any;
}

export function PrintableVehicleHistoryReport({
  vehicleId,
  historyData,
  isLoading = false,
  history: initialHistory,
}: PrintableVehicleHistoryReportProps) {
  const [history, setHistory] = useState<any>(initialHistory || null);
  const [loading, setLoading] = useState(isLoading);
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    if (historyData) {
      setHistory(historyData);
      setLoading(false);
    }
  }, [historyData]);

  const handlePrint = () => {
    setIsPrinting(true);
    setTimeout(() => {
      window.print();
      setIsPrinting(false);
    }, 100);
  };

  const handleExport = () => {
    if (!history) return;

    // Create a JSON blob and download it
    const dataStr = JSON.stringify(history, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `vehicle_history_${history.vin || 'unknown'}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Vehicle history exported successfully');
  };

  const handleExportPDF = () => {
    toast.info('PDF export functionality will be implemented in a future update');
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle History Report</CardTitle>
          <CardDescription>Loading vehicle history...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-6">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!history) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle History Report</CardTitle>
          <CardDescription>No history report available</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-neutral-500">
            No history report is available for this vehicle. You can request a history report to view detailed information.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Helper function to get severity badge
  const getSeverityBadge = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'minor':
        return <Badge variant="outline">Minor</Badge>;
      case 'moderate':
        return <Badge variant="secondary">Moderate</Badge>;
      case 'major':
        return <Badge variant="destructive">Major</Badge>;
      case 'severe':
        return <Badge variant="destructive" className="bg-red-700">Severe</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6 print:m-0 print:p-0 print:shadow-none">
      <div className="flex justify-between items-center print:hidden">
        <h2 className="text-xl font-bold">Vehicle History Report</h2>
        <div className="flex gap-2">
          <Button onClick={handlePrint} disabled={isPrinting} variant="outline" className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            <span>{isPrinting ? 'Preparing...' : 'Print Report'}</span>
          </Button>
          <Button onClick={handleExport} variant="outline" className="flex items-center gap-2">
            <FileDown className="h-4 w-4" />
            <span>Export JSON</span>
          </Button>
          <Button onClick={handleExportPDF} variant="default" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            <span>Export PDF</span>
          </Button>
        </div>
      </div>

      <div className="print:mt-0">
        <Card className="print:shadow-none print:border-none">
          <CardHeader className="print:pb-2">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-4 mb-2">
                  <div className="print:block hidden">
                    <Image
                      src="/logo.png"
                      alt="VHRS Logo"
                      width={80}
                      height={80}
                      className="print:block hidden"
                      onError={(e) => {
                        // Hide the image if it fails to load
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">
                      {history.make} {history.model} ({history.year})
                    </CardTitle>
                    <CardDescription>
                      VIN: {history.vin} • Type: {history.type} • Generated: {formatDate(history.generated_at)}
                    </CardDescription>
                  </div>
                </div>
              </div>
              <div className="print:hidden">
                <Badge variant="outline" className="text-sm">
                  Official Report
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6 print:space-y-4">
            {/* Vehicle Information */}
            <div className="print:mt-4">
              <h3 className="text-lg font-semibold mb-2 print:text-base">Vehicle Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 print:grid-cols-3">
                <div className="space-y-1 border rounded-md p-3 print:border-0 print:p-1">
                  <p className="text-sm font-medium text-neutral-500">VIN</p>
                  <p className="font-mono">{history.vin || 'Not available'}</p>
                </div>
                <div className="space-y-1 border rounded-md p-3 print:border-0 print:p-1">
                  <p className="text-sm font-medium text-neutral-500">Make & Model</p>
                  <p>{history.make} {history.model}</p>
                </div>
                <div className="space-y-1 border rounded-md p-3 print:border-0 print:p-1">
                  <p className="text-sm font-medium text-neutral-500">Year</p>
                  <p>{history.year}</p>
                </div>
                <div className="space-y-1 border rounded-md p-3 print:border-0 print:p-1">
                  <p className="text-sm font-medium text-neutral-500">Type</p>
                  <p>{history.type || 'Not specified'}</p>
                </div>
                <div className="space-y-1 border rounded-md p-3 print:border-0 print:p-1">
                  <p className="text-sm font-medium text-neutral-500">Color</p>
                  <p>{history.color || 'Not specified'}</p>
                </div>
                <div className="space-y-1 border rounded-md p-3 print:border-0 print:p-1">
                  <p className="text-sm font-medium text-neutral-500">Engine</p>
                  <p>{history.engine || 'Not specified'}</p>
                </div>
              </div>
            </div>

            {/* Ownership History Section */}
            <div>
              <h3 className="text-lg font-semibold mb-2 print:text-base">Ownership History</h3>
              {history.ownership && history.ownership.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Owner</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>From</TableHead>
                      <TableHead>To</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {history.ownership.map((record: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{record.owner_name}</TableCell>
                        <TableCell>{record.owner_phone}</TableCell>
                        <TableCell>{formatDate(record.start_date)}</TableCell>
                        <TableCell>{record.end_date ? formatDate(record.end_date) : 'Present'}</TableCell>
                        <TableCell>
                          {record.is_current ? (
                            <Badge variant="default">Current</Badge>
                          ) : (
                            <Badge variant="outline">Previous</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-neutral-500 italic">No ownership records available</p>
              )}
            </div>

            {/* License Plate History */}
            <div>
              <h3 className="text-lg font-semibold mb-2 print:text-base">License Plate History</h3>
              {history.license_plates && history.license_plates.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plate Number</TableHead>
                      <TableHead>Issued Date</TableHead>
                      <TableHead>Expiry Date</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {history.license_plates.map((record: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{record.plate_number}</TableCell>
                        <TableCell>{formatDate(record.issued_date)}</TableCell>
                        <TableCell>{record.expiry_date ? formatDate(record.expiry_date) : 'N/A'}</TableCell>
                        <TableCell>
                          {record.is_current ? (
                            <Badge variant="default">Current</Badge>
                          ) : (
                            <Badge variant="outline">Previous</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-neutral-500 italic">No license plate records available</p>
              )}
            </div>

            {/* Maintenance History */}
            <div>
              <h3 className="text-lg font-semibold mb-2 print:text-base">Maintenance History</h3>
              {history.maintenance && history.maintenance.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Service Type</TableHead>
                      <TableHead>Mileage</TableHead>
                      <TableHead>Service Provider</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {history.maintenance.map((record: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{formatDate(record.service_date)}</TableCell>
                        <TableCell className="font-medium">{record.service_type}</TableCell>
                        <TableCell>{record.mileage ? `${record.mileage} km` : 'N/A'}</TableCell>
                        <TableCell>{record.service_provider || 'Not recorded'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-neutral-500 italic">No maintenance records available</p>
              )}
            </div>

            {/* Accident History Section */}
            <div>
              <h3 className="text-lg font-semibold mb-2 print:text-base">Accident History</h3>
              {history.accidents && history.accidents.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {history.accidents.map((record: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell>
                          {getSeverityBadge(record.severity)}
                        </TableCell>
                        <TableCell>
                          {record.description || 'No additional details'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-neutral-500 italic">No accident records available</p>
              )}
            </div>

            {/* Mileage History */}
            <div>
              <h3 className="text-lg font-semibold mb-2 print:text-base">Mileage History</h3>
              {history.mileage && history.mileage.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Reading</TableHead>
                      <TableHead>Source</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {history.mileage.map((record: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell className="font-medium">{record.reading} km</TableCell>
                        <TableCell>{record.source || 'Not specified'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-neutral-500 italic">No mileage records available</p>
              )}
            </div>

            <div className="border-t pt-4 print:mt-8">
              <p className="text-sm text-neutral-500">
                This vehicle history report was generated on {formatDate(history.generated_at)} by the Vehicle History Record System.
                This document serves as an official report of the vehicle's history as recorded in the system.
              </p>
              <p className="text-sm text-neutral-500 mt-2">
                Document ID: {history.id || vehicleId || 'Unknown'}-{new Date().toISOString().split('T')[0]}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <style jsx global>{`
        @media print {
          @page {
            size: A4;
            margin: 1cm;
          }

          body * {
            visibility: hidden;
          }

          .print\\:shadow-none,
          .print\\:shadow-none * {
            visibility: visible;
          }

          .print\\:shadow-none {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }

          .print\\:hidden {
            display: none !important;
          }

          .print\\:block {
            display: block !important;
          }

          .print\\:border-0 {
            border: none !important;
          }

          .print\\:p-1 {
            padding: 0.25rem !important;
          }

          .print\\:grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
          }

          .print\\:text-base {
            font-size: 1rem !important;
          }

          .print\\:mt-4 {
            margin-top: 1rem !important;
          }

          .print\\:mt-8 {
            margin-top: 2rem !important;
          }

          .print\\:pb-2 {
            padding-bottom: 0.5rem !important;
          }

          .print\\:space-y-4 > * + * {
            margin-top: 1rem !important;
          }
        }
      `}</style>
    </div>
  );
}
