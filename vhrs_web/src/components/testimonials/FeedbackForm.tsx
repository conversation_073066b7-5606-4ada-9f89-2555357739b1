'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { zodResolver } from '@hookform/resolvers/zod';
import { MessageSquarePlus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Form validation schema
const feedbackSchema = z.object({
  full_name: z.string().min(2, 'Name must be at least 2 characters'),
  location: z.string().optional(),
  content: z.string().min(10, 'Feedback must be at least 10 characters').max(500, 'Feedback must be less than 500 characters'),
});

type FeedbackFormValues = z.infer<typeof feedbackSchema>;

export function FeedbackForm() {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();

  // Initialize form with react-hook-form
  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackSchema),
    defaultValues: {
      full_name: user?.user_metadata?.full_name || '',
      location: '',
      content: '',
    },
  });

  // Handle form submission
  const onSubmit = async (values: FeedbackFormValues) => {
    if (!user) {
      toast.error('You must be logged in to submit feedback');
      return;
    }

    try {
      setIsSubmitting(true);

      // Try to submit via the Edge Function
      const { error } = await supabase.functions.invoke('manage_testimonials', {
        body: {
          action: 'submit',
          testimonial_data: values,
        },
      });

      if (error) {
        // Fallback to direct database insertion if function fails
        const { error: insertError } = await supabase
          .from('testimonials')
          .insert({
            user_id: user.id,
            full_name: values.full_name,
            location: values.location,
            content: values.content,
            rating: values.rating,
            status: 'pending',
          });

        if (insertError) throw insertError;
      }

      toast.success('Thank you for your feedback! It will be reviewed shortly.');
      form.reset();
      setOpen(false);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={() => {
            if (!user) {
              toast.error('Please sign in to share your feedback');
              return;
            }
            setOpen(true);
          }}
        >
          <MessageSquarePlus className="h-4 w-4" />
          <span>Share Your Experience</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Your Experience</DialogTitle>
          <DialogDescription>
            Tell us about your experience with our vehicle history record system. Your feedback helps us improve and helps others make informed decisions.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="full_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Car Buyer, Kampala" {...field} />
                  </FormControl>
                  <FormDescription>
                    This helps others understand your perspective
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />



            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your Feedback</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Share your experience with our system..."
                      className="min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Maximum 500 characters
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
