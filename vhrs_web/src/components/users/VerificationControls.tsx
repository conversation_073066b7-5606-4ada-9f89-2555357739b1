'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { supabase } from '@/lib/supabase';
import { CheckCircle, XCircle } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface VerificationControlsProps {
  userId: string;
  userEmail: string;
  isVerified: boolean;
  onVerificationChange: () => void;
}

export function VerificationControls({
  userId,
  userEmail,
  isVerified,
  onVerificationChange,
}: VerificationControlsProps) {
  const [open, setOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [action, setAction] = useState<'verify' | 'unverify'>('verify');

  const handleOpenDialog = (actionType: 'verify' | 'unverify') => {
    setAction(actionType);
    setOpen(true);
  };

  const handleVerificationChange = async () => {
    setIsProcessing(true);
    try {
      // Update the user's verification status
      const { error } = await supabase
        .from('profiles')
        .update({ is_verified: action === 'verify' })
        .eq('id', userId);

      if (error) throw error;

      // If verifying, also update auth.users (requires admin privileges)
      if (action === 'verify') {
        const { error: authError } = await supabase.auth.admin.updateUserById(userId, {
          email_confirm: true,
        });

        if (authError) throw authError;
      }

      toast.success(
        action === 'verify'
          ? 'User has been verified successfully'
          : 'User verification has been revoked'
      );
      setOpen(false);
      onVerificationChange();
    } catch (error) {
      console.error('Error updating verification status:', error);
      toast.error('Failed to update verification status');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex gap-2">
      {isVerified ? (
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          onClick={() => handleOpenDialog('unverify')}
        >
          <XCircle className="h-4 w-4" />
          <span>Revoke Verification</span>
        </Button>
      ) : (
        <Button
          variant="default"
          size="sm"
          className="flex items-center gap-2"
          onClick={() => handleOpenDialog('verify')}
        >
          <CheckCircle className="h-4 w-4" />
          <span>Verify User</span>
        </Button>
      )}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {action === 'verify' ? 'Verify User' : 'Revoke Verification'}
            </DialogTitle>
            <DialogDescription>
              {action === 'verify'
                ? `This will mark ${userEmail} as verified, allowing full access to the system.`
                : `This will revoke verification status for ${userEmail}, which may limit their access.`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button
              variant={action === 'verify' ? 'default' : 'destructive'}
              onClick={handleVerificationChange}
              disabled={isProcessing}
            >
              {isProcessing
                ? 'Processing...'
                : action === 'verify'
                ? 'Verify User'
                : 'Revoke Verification'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
