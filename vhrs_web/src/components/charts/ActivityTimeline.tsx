'use client';

import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/lib/supabase';
import { useEffect, useState } from 'react';
import {
    Area,
    AreaChart,
    CartesianGrid,
    Legend,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis,
} from 'recharts';

type ActivityData = {
  date: string;
  vehicles: number;
  alerts: number;
  requests: number;
};

export function ActivityTimeline() {
  const [data, setData] = useState<ActivityData[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7days' | '30days' | '90days'>('30days');

  useEffect(() => {
    fetchActivityData();
  }, [timeRange]);

  const fetchActivityData = async () => {
    setLoading(true);
    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();

      if (timeRange === '7days') {
        startDate.setDate(endDate.getDate() - 7);
      } else if (timeRange === '30days') {
        startDate.setDate(endDate.getDate() - 30);
      } else {
        startDate.setDate(endDate.getDate() - 90);
      }

      // Format dates for Supabase queries
      const startDateStr = startDate.toISOString();
      const endDateStr = endDate.toISOString();

      // Get activity data from Supabase
      const [vehiclesRes, alertsRes, requestsRes] = await Promise.all([
        supabase
          .from('vehicles')
          .select('created_at')
          .gte('created_at', startDateStr)
          .lte('created_at', endDateStr),
        supabase
          .from('alerts')
          .select('created_at')
          .gte('created_at', startDateStr)
          .lte('created_at', endDateStr),
        supabase
          .from('history_report_requests')
          .select('created_at')
          .gte('created_at', startDateStr)
          .lte('created_at', endDateStr),
      ]);

      // Process data for the chart
      const vehiclesData = vehiclesRes.data || [];
      const alertsData = alertsRes.data || [];
      const requestsData = requestsRes.data || [];

      // Group by date
      const dateMap = new Map<string, ActivityData>();

      // Initialize dates in the range
      let currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        dateMap.set(dateStr, {
          date: dateStr,
          vehicles: 0,
          alerts: 0,
          requests: 0,
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Count vehicles by date
      vehiclesData.forEach((item) => {
        const dateStr = new Date(item.created_at).toISOString().split('T')[0];
        if (dateMap.has(dateStr)) {
          const entry = dateMap.get(dateStr)!;
          entry.vehicles += 1;
          dateMap.set(dateStr, entry);
        }
      });

      // Count alerts by date
      alertsData.forEach((item) => {
        const dateStr = new Date(item.created_at).toISOString().split('T')[0];
        if (dateMap.has(dateStr)) {
          const entry = dateMap.get(dateStr)!;
          entry.alerts += 1;
          dateMap.set(dateStr, entry);
        }
      });

      // Count requests by date
      requestsData.forEach((item) => {
        const dateStr = new Date(item.created_at).toISOString().split('T')[0];
        if (dateMap.has(dateStr)) {
          const entry = dateMap.get(dateStr)!;
          entry.requests += 1;
          dateMap.set(dateStr, entry);
        }
      });

      // Convert map to array and sort by date
      const chartData = Array.from(dateMap.values()).sort((a, b) =>
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      setData(chartData);
    } catch (error) {
      console.error('Error fetching activity data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return `${date.getDate()}/${date.getMonth() + 1}`;
  };

  const handleTimeRangeChange = (range: '7days' | '30days' | '90days') => {
    setTimeRange(range);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-64" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
          <div>
            <CardTitle>Activity Timeline</CardTitle>
            <CardDescription>System activity over time</CardDescription>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handleTimeRangeChange('7days')}
              className={`px-2 py-1 text-xs rounded ${
                timeRange === '7days'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary text-secondary-foreground'
              }`}
            >
              7 Days
            </button>
            <button
              onClick={() => handleTimeRangeChange('30days')}
              className={`px-2 py-1 text-xs rounded ${
                timeRange === '30days'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary text-secondary-foreground'
              }`}
            >
              30 Days
            </button>
            <button
              onClick={() => handleTimeRangeChange('90days')}
              className={`px-2 py-1 text-xs rounded ${
                timeRange === '90days'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary text-secondary-foreground'
              }`}
            >
              90 Days
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[250px] sm:h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{
                top: 10,
                right: 20,
                left: 0,
                bottom: 0,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" tickFormatter={formatDate} />
              <YAxis />
              <Tooltip
                formatter={(value, name) => [value, name.charAt(0).toUpperCase() + name.slice(1)]}
                labelFormatter={(label) => new Date(label).toLocaleDateString()}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey="vehicles"
                stackId="1"
                stroke="#8884d8"
                fill="#8884d8"
                name="Vehicles"
              />
              <Area
                type="monotone"
                dataKey="alerts"
                stackId="1"
                stroke="#ffc658"
                fill="#ffc658"
                name="Alerts"
              />
              <Area
                type="monotone"
                dataKey="requests"
                stackId="1"
                stroke="#82ca9d"
                fill="#82ca9d"
                name="Requests"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
