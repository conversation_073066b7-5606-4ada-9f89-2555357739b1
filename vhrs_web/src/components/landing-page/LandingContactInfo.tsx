'use client';

import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { Mail, Phone } from 'lucide-react';

export function LandingContactInfo() {
  const { settings, loading } = useSystemSettings();

  if (loading) {
    return (
      <div className="flex flex-col gap-2 text-sm text-neutral-500">
        <div className="h-5 w-40 animate-pulse rounded bg-neutral-200"></div>
        <div className="h-5 w-32 animate-pulse rounded bg-neutral-200"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-2 text-sm text-neutral-500">
      {settings.contact_email && (
        <a
          href={`mailto:${settings.contact_email}`}
          className="flex items-center gap-2 hover:text-neutral-800 transition-colors"
        >
          <Mail className="h-4 w-4" />
          <span>{settings.contact_email}</span>
        </a>
      )}
      {settings.support_phone && (
        <a
          href={`tel:${settings.support_phone}`}
          className="flex items-center gap-2 hover:text-neutral-800 transition-colors"
        >
          <Phone className="h-4 w-4" />
          <span>{settings.support_phone}</span>
        </a>
      )}
    </div>
  );
}
