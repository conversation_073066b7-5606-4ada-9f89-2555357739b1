'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

type AnimatedBackgroundProps = {
  children: ReactNode;
  className?: string;
};

export function AnimatedBackground({ children, className = '' }: AnimatedBackgroundProps) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Animated background shapes */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large circle */}
        <motion.div
          className="absolute -top-[10%] -right-[10%] w-[40%] h-[40%] rounded-full bg-primary/5"
          animate={{
            scale: [1, 1.05, 1],
            x: [0, 10, 0],
            y: [0, -10, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
        
        {/* Medium circle */}
        <motion.div
          className="absolute top-[60%] -left-[5%] w-[25%] h-[25%] rounded-full bg-primary/3"
          animate={{
            scale: [1, 1.1, 1],
            x: [0, 15, 0],
            y: [0, 10, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: 1,
          }}
        />
        
        {/* Small circle */}
        <motion.div
          className="absolute top-[20%] left-[10%] w-[15%] h-[15%] rounded-full bg-primary/4"
          animate={{
            scale: [1, 1.15, 1],
            x: [0, 20, 0],
            y: [0, 20, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: 2,
          }}
        />
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}
