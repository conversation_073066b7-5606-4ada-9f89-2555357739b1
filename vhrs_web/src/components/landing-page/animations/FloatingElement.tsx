'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

type FloatingElementProps = {
  children: ReactNode;
  amplitude?: number;
  duration?: number;
  delay?: number;
  className?: string;
};

export function FloatingElement({ 
  children, 
  amplitude = 10, 
  duration = 4,
  delay = 0,
  className = '' 
}: FloatingElementProps) {
  return (
    <motion.div
      animate={{ 
        y: [0, -amplitude, 0, amplitude, 0],
      }}
      transition={{
        duration: duration,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeInOut",
        delay: delay
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
