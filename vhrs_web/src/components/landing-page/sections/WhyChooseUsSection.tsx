'use client';

import { AnimatedSection } from '@/components/landing-page';
import { motion } from 'framer-motion';

export function WhyChooseUsSection() {
  return (
    <div className="mb-20 relative">
      {/* Background decorative elements */}
      <motion.div
        className="absolute top-40 -left-20 w-40 h-40 rounded-full bg-primary/5 opacity-70"
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 15, 0],
        }}
        transition={{
          duration: 18,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
      />

      <AnimatedSection>
        <div className="text-center mb-16 relative z-10">
          <motion.h2
            className="text-3xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Why Choose Our System
          </motion.h2>
          <motion.div
            className="h-1 w-20 bg-gradient-to-r from-primary/60 via-primary to-primary/60 mx-auto mb-6 rounded-full"
            initial={{ opacity: 0, scaleX: 0 }}
            whileInView={{ opacity: 1, scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.1 }}
          />
          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <p className="text-xl text-gray-600">Our platform provides comprehensive vehicle history information with unmatched reliability and ease of use.</p>
          </motion.div>
        </div>
      </AnimatedSection>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
        <FeatureCard
          title="Trusted Vehicle Data"
          description="Access comprehensive and verified vehicle history information from official sources, ensuring you make informed decisions."
          icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />}
          delay={0.1}
        />

        <FeatureCard
          title="Secure & Transparent"
          description="Our system ensures data integrity and security while providing complete transparency in vehicle history reporting."
          icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />}
          delay={0.2}
        />

        <FeatureCard
          title="Comprehensive Reports"
          description="Get detailed reports covering ownership history, accident records, service history, and more in one easy-to-read format."
          icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />}
          delay={0.3}
        />
      </div>
    </div>
  );
}

interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  delay?: number;
}

function FeatureCard({ title, description, icon, delay = 0 }: FeatureCardProps) {
  return (
    <AnimatedSection delay={delay}>
      <motion.div
        className="flex flex-col items-center text-center group"
        whileHover={{
          y: -8,
          transition: { type: "spring", stiffness: 300, damping: 15 }
        }}
      >
        <motion.div
          className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6 relative overflow-hidden"
          whileHover={{
            scale: 1.1,
            backgroundColor: "rgba(var(--color-primary), 0.15)",
          }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <motion.div
            className="absolute inset-0 bg-primary/5 rounded-full"
            animate={{
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
              delay: delay * 0.5,
            }}
          />
          <motion.svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-primary relative z-10"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            whileHover={{ rotate: 15 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {icon}
          </motion.svg>
        </motion.div>
        <h3 className="text-2xl font-bold text-gray-900 mb-3">{title}</h3>
        <p className="text-gray-600 leading-relaxed">
          {description}
        </p>
      </motion.div>
    </AnimatedSection>
  );
}
