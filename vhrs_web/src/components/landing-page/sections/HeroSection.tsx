'use client';

import { AnimatedBackground, FadeIn, MaintenanceMode, TrustedByGovCard } from '@/components/landing-page';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import Link from 'next/link';

export function HeroSection() {
  return (
    <AnimatedBackground className="pt-20 pb-24 relative bg-gradient-to-br from-primary/5 to-primary/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <MaintenanceMode />

        <div className="flex flex-col md:flex-row items-center justify-between gap-12">
          <div className="md:w-1/2 text-center md:text-left">
            <FadeIn direction="up">
              <h2 className="text-4xl md:text-5xl font-extrabold text-gray-900 leading-tight">
                Uganda Vehicle <span className="text-primary relative">
                  History Record
                  <motion.span
                    className="absolute bottom-0 left-0 w-full h-[6px] bg-primary/20 rounded-full"
                    initial={{ scaleX: 0, originX: 0 }}
                    animate={{ scaleX: 1 }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />
                </span> System
              </h2>
            </FadeIn>
            <FadeIn direction="up" delay={0.2}>
              <p className="mt-6 text-xl text-gray-600 max-w-2xl">
                A comprehensive system for tracking and verifying vehicle history reports in Uganda, ensuring transparency and trust in the automotive market.
              </p>
            </FadeIn>
            <FadeIn direction="up" delay={0.4}>
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                <Link href="/auth/signin?redirect=/dashboard/vehicles">
                  <Button
                    size="lg"
                    className="px-8 relative overflow-hidden group"
                  >
                    <span className="relative z-10">Get Started</span>
                    <motion.span
                      className="absolute inset-0 bg-primary/80 transform"
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.3 }}
                    />
                  </Button>
                </Link>
                <Link href="#learn-more">
                  <Button
                    size="lg"
                    variant="outline"
                    className="px-8 relative overflow-hidden group border-primary/50 hover:border-primary"
                  >
                    <span className="relative z-10 group-hover:text-white transition-colors duration-300">Learn More</span>
                    <span className="absolute inset-0 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
                  </Button>
                </Link>
              </div>

              {/* System Coverage List */}
              <motion.div
                className="mt-10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                {/* Users indicator */}
                <div className="flex items-center justify-center md:justify-start mb-4">
                  <div className="flex items-center px-3 py-1.5 bg-primary/20 backdrop-blur-sm rounded-full border border-primary/30">
                    <svg className="w-4 h-4 text-primary mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
                    </svg>
                    <span className="text-primary text-xs font-medium">Trusted by</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-6 justify-center md:justify-start">
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                    <span className="text-black/80 text-sm">Vehicle Buyers</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                    <span className="text-black/80 text-sm">Police Stations</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                    <span className="text-black/80 text-sm">Insurance Companies</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                    <span className="text-black/80 text-sm">Car Dealers</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
                    <span className="text-black/80 text-sm">Vehicle Owners</span>
                  </div>
                </div>
              </motion.div>
            </FadeIn>
          </div>
          <div className="md:w-1/2 flex justify-center">
            <TrustedByGovCard />
          </div>
        </div>
      </div>
    </AnimatedBackground>
  );
}
