'use client';

import { FloatingElement } from '@/components/landing-page';
import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { supabase } from '@/lib/supabase';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface SystemStats {
  totalVehicles: number;
  totalUsers: number;
  totalAlerts: number;
  totalHistoryRequests: number;
}

export function TrustedByGovCard() {
  const { settings: systemInfo, loading: loadingSystemInfo } = useSystemSettings();
  const [stats, setStats] = useState<SystemStats>({
    totalVehicles: 0,
    totalUsers: 0,
    totalAlerts: 0,
    totalHistoryRequests: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [
          { count: vehiclesCount },
          { count: usersCount },
          { count: alertsCount },
          { count: historyRequestsCount },
        ] = await Promise.all([
          supabase.from('vehicles').select('*', { count: 'exact', head: true }),
          supabase.from('profiles').select('*', { count: 'exact', head: true }),
          supabase.from('alerts').select('*', { count: 'exact', head: true }),
          supabase.from('history_report_requests').select('*', { count: 'exact', head: true }),
        ]);

        setStats({
          totalVehicles: vehiclesCount || 0,
          totalUsers: usersCount || 0,
          totalAlerts: alertsCount || 0,
          totalHistoryRequests: historyRequestsCount || 0,
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Set some default values for demo purposes
        setStats({
          totalVehicles: 15420,
          totalUsers: 2847,
          totalAlerts: 1256,
          totalHistoryRequests: 8934,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <FloatingElement amplitude={8} duration={5}>
      <div className="relative w-full max-w-lg">
        {/* Animated background circles - positioned outside card boundaries */}
        <motion.div
          className="absolute -top-6 -right-6 w-16 h-16 rounded-full bg-primary/20 z-10"
          animate={{ rotate: 360 }}
          transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute -bottom-4 -left-4 w-12 h-12 rounded-full bg-white/20 z-10"
          animate={{ rotate: -360 }}
          transition={{ duration: 45, repeat: Infinity, ease: "linear" }}
        />

        {/* Card Container - handles hover for both cards */}
        <motion.div
          className="relative"
          whileHover={{
            y: -8,
          }}
          transition={{ duration: 0.4, ease: "easeIn" }}
        >
          {/* Base Card (Primary gradient) - positioned behind */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-primary via-primary/90 to-primary/80 rounded-2xl shadow-2xl"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          />

          {/* Glass Card */}
          <motion.div
            className="relative bg-gradient-to-br from-white/25 via-white/15 to-white/10 backdrop-blur-xl rounded-2xl border border-white/40 shadow-2xl overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            whileHover={{
              boxShadow: "0 32px 64px -12px rgba(0, 0, 0, 0.2)",
            }}
          >
          {/* Border overlay effects */}
          <div className="absolute inset-0 rounded-2xl border border-white/50 [mask-image:linear-gradient(135deg,white,transparent_50%)]" />
          <div className="absolute inset-0 rounded-2xl border border-primary/30 [mask-image:linear-gradient(135deg,transparent_50%,white)]" />

          {/* Content */}
          <div className="relative p-8 text-white">
            {/* Header */}
            <div className="flex justify-between items-start mb-6">
              <div className="flex-1">
                <h3 className="text-2xl font-bold leading-tight">
                  Trusted by Government Agencies
                </h3>
                <p className="text-white/80 mt-2 text-sm">
                  Official vehicle records from authorized sources
                </p>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold text-white/90">{systemInfo.system_name}</div>
                <div className="text-xs text-white/70">v1.0</div>
              </div>
            </div>

            {/* Icon strip */}
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center border border-white/30">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="w-px h-6 bg-white/30" />
              <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center border border-white/30">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
              </div>
              <div className="w-px h-6 bg-white/30" />
              <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center border border-white/30">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                </svg>
              </div>
            </div>

            {/* Main divider */}
            <div className="h-px bg-gradient-to-r from-transparent via-white/40 to-transparent mb-6" />

            {/* Stats Section */}
            <div className="mb-6">
              {/* Top row - with vertical dividers */}
              <div className="flex justify-between items-center mb-4 max-w-sm mx-auto">
                <div className="text-center px-2">
                  <div className="text-2xl font-bold text-white mb-1">
                    {loading ? (
                      <div className="animate-pulse bg-white/20 h-6 w-12 mx-auto rounded"></div>
                    ) : (
                      <motion.span
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.8 }}
                      >
                        {stats.totalVehicles.toLocaleString()}
                      </motion.span>
                    )}
                  </div>
                  <div className="text-xs text-white/70 uppercase tracking-wide">Vehicles</div>
                </div>

                <div className="w-px h-12 bg-gradient-to-b from-transparent via-white/40 to-transparent"></div>

                <div className="text-center px-2">
                  <div className="text-2xl font-bold text-white mb-1">
                    {loading ? (
                      <div className="animate-pulse bg-white/20 h-6 w-12 mx-auto rounded"></div>
                    ) : (
                      <motion.span
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.9 }}
                      >
                        {stats.totalUsers.toLocaleString()}
                      </motion.span>
                    )}
                  </div>
                  <div className="text-xs text-white/70 uppercase tracking-wide">Users</div>
                </div>
              </div>

              {/* Bottom row - with cards */}
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 rounded-xl bg-primary/25 border border-white/20">
                  <div className="text-xl font-bold text-white/95 mb-1">
                    {loading ? (
                      <div className="animate-pulse bg-white/20 h-5 w-12 mx-auto rounded"></div>
                    ) : (
                      <motion.span
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 1.0 }}
                      >
                        {stats.totalAlerts.toLocaleString()}
                      </motion.span>
                    )}
                  </div>
                  <div className="text-xs text-white/90 uppercase tracking-wide">Alerts</div>
                </div>

                <div className="text-center p-3 rounded-xl bg-primary/25 border border-white/20">
                  <div className="text-xl font-bold text-white/95 mb-1">
                    {loading ? (
                      <div className="animate-pulse bg-white/20 h-5 w-12 mx-auto rounded"></div>
                    ) : (
                      <motion.span
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 1.1 }}
                      >
                        {stats.totalHistoryRequests.toLocaleString()}
                      </motion.span>
                    )}
                  </div>
                  <div className="text-xs text-white/90 uppercase tracking-wide">Records Checked</div>
                </div>
              </div>
            </div>

            {/* Secondary divider */}
            <div className="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent mb-4" />

            {/* Feature tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {[
                { text: 'ISO 27001 CERTIFIED', delay: 1.2 },
                { text: 'FRAUD DETECTION', delay: 1.3 },
                { text: 'REAL-TIME PROCESSING', delay: 1.4 },
                { text: 'GOVERNMENT APPROVED', delay: 1.5 },
              ].map((tag, index) => (
                <motion.span
                  key={tag.text}
                  className="text-xs px-3 py-1 rounded-full bg-primary/30 border border-white/30 hover:bg-primary/40 transition-colors cursor-default text-white/95"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: tag.delay }}
                  whileHover={{ scale: 1.05 }}
                >
                  {tag.text}
                </motion.span>
              ))}
            </div>

            {/* Footer details */}
            <div className="flex justify-between items-end text-xs">
              <div className="flex flex-col gap-1">
                <span className="text-white/80 flex items-center gap-1">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Security Certified
                </span>
                <span className="text-white/60 flex items-center gap-1">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-0.257-0.257A6 6 0 1118 8zM10 2a1 1 0 011 1v1.267a4.39 4.39 0 011.617.602l.894-.894a1 1 0 011.414 1.414l-.894.894A4.39 4.39 0 0115.732 8H17a1 1 0 110 2h-1.268a4.39 4.39 0 01-.601 1.617l.894.894a1 1 0 11-1.414 1.414l-.894-.894A4.39 4.39 0 0112 13.732V15a1 1 0 11-2 0v-1.268a4.39 4.39 0 01-1.617-.601l-.894.894a1 1 0 01-1.414-1.414l.894-.894A4.39 4.39 0 014.268 10H3a1 1 0 110-2h1.268a4.39 4.39 0 01.601-1.617L4.025 5.54a1 1 0 011.414-1.414l.894.894A4.39 4.39 0 018 4.268V3a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  ID: UG-AUTOFRANK-2025
                </span>
              </div>

              <div className="flex flex-col items-end gap-1">
                <span className="text-white/80 flex items-center gap-1">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Uptime 99.9%
                </span>
                <span className="text-white/90 font-medium flex items-center gap-1">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                  </svg>
                  autofrank.com
                </span>
              </div>
            </div>
          </div>
        </motion.div>
        </motion.div>
      </div>
    </FloatingElement>
  );
}
