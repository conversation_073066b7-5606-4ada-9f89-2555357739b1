'use client';

import { AnimatedSection } from '@/components/landing-page';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import Link from 'next/link';

export function VehicleHistoryCheckCard() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative -mt-8 mb-16">
      <AnimatedSection>
        <motion.div
          className="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100"
          whileHover={{
            boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)",
            y: -5
          }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
        >
          <div className="p-6 md:p-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              <div className="text-center md:text-left">
                <div className="flex items-center gap-3 mb-2">
                  <motion.div
                    whileHover={{ rotate: 15, scale: 1.2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </motion.div>
                  <h3 className="text-2xl font-bold text-gray-900">Check Vehicle History</h3>
                </div>
                <p className="text-gray-600">Enter a license plate or VIN to check a vehicle's history record</p>
              </div>
              <div className="w-full md:w-auto flex-1 max-w-md">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="flex-1 relative">
                    <motion.input
                      type="text"
                      placeholder="Enter license plate or VIN"
                      className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                      whileFocus={{ scale: 1.02 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    />
                    <motion.div
                      className="absolute bottom-0 left-0 h-[2px] bg-primary"
                      initial={{ width: 0 }}
                      whileFocus={{ width: "100%" }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                  <Link href="/auth/signin?redirect=/dashboard/vehicles/request">
                    <Button
                      className="w-full sm:w-auto whitespace-nowrap relative overflow-hidden group"
                    >
                      <span className="relative z-10">Check History</span>
                      <span className="absolute inset-0 bg-primary/80 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatedSection>
    </div>
  );
}
