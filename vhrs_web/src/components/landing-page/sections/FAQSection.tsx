'use client';

import { AnimatedSection } from '@/components/landing-page';
import { Button } from '@/components/ui/button';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import { motion } from 'framer-motion';
import { useState } from 'react';

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: "How do I request a vehicle history report?",
    answer: "Simply create an account on our platform, navigate to the vehicle search section, and enter the vehicle's VIN, plate number, or chassis number. Our system will process your request and provide a comprehensive history report within minutes."
  },
  {
    question: "What information is included in a vehicle history report?",
    answer: "Our reports include ownership history, accident records, theft reports, insurance claims, maintenance records, import/export data, and any liens or encumbrances. All data is sourced from official government databases and authorized institutions."
  },
  {
    question: "How secure is my personal and vehicle data?",
    answer: "We employ bank-level security with end-to-end encryption, ISO 27001 certification, and strict compliance with data protection regulations. All data is stored in secure, government-approved facilities with 24/7 monitoring and regular security audits."
  },
  {
    question: "Can I verify the authenticity of a vehicle history report?",
    answer: "Yes, every report includes a unique verification code and digital signature. You can verify the authenticity of any report through our verification portal or by contacting our support team. All reports are traceable and tamper-proof."
  },
  {
    question: "What types of vehicles are supported by the system?",
    answer: "Our system supports all registered vehicles in Uganda including cars, motorcycles, trucks, buses, trailers, and commercial vehicles. We maintain records for both locally assembled and imported vehicles from all major manufacturers."
  },
  {
    question: "How quickly can I receive a vehicle history report?",
    answer: "Most reports are generated instantly for vehicles with complete records. In some cases where additional verification is required, reports may take up to 24 hours. You'll receive email notifications about your request status and report availability."
  }
];

export function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <div className="mb-20 relative">
      {/* Background illumination effects */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-20 left-1/4 w-32 h-32 rounded-full bg-primary/10 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-20 right-1/4 w-40 h-40 rounded-full bg-primary/8 blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: 2,
          }}
        />
      </div>

      {/* Two-column layout for large screens, vertical for smaller screens */}
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 lg:gap-16 items-start">
          {/* Left Column - Title and Description */}
          <AnimatedSection className="lg:col-span-2">
            <div className="text-center lg:text-left">
              {/* Animated Vehicle Illustration */}
              <motion.div
                className="mb-8 flex justify-center lg:justify-start"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.1 }}
              >
                <div className="relative">
                  <DotLottieReact
                    src="https://lottie.host/43d251c3-4be6-47d5-9108-c326370ee3b3/T7smYxBf8l.lottie"
                    loop
                    autoplay
                    className="opacity-80 w-64 h-36"
                  />

                  {/* Floating geometric elements */}
                  <motion.div
                    className="absolute -top-2 -right-2 w-3 h-3 bg-primary/30 rounded-full"
                    animate={{
                      y: [0, -8, 0],
                      opacity: [0.3, 0.8, 0.3],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      repeatType: "reverse",
                      ease: "easeInOut",
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-1 -left-3 w-2 h-2 bg-primary/40 rotate-45"
                    animate={{
                      rotate: [45, 90, 45],
                      scale: [1, 1.2, 1],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      repeatType: "reverse",
                      ease: "easeInOut",
                      delay: 1,
                    }}
                  />
                </div>
              </motion.div>

              <motion.h2
                className="text-4xl font-bold text-gray-900 mb-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                Frequently Asked Questions
              </motion.h2>
              <motion.div
                className="h-1 w-20 bg-gradient-to-r from-primary/60 via-primary to-primary/60 mx-auto lg:mx-0 mb-6 rounded-full"
                initial={{ opacity: 0, scaleX: 0 }}
                whileInView={{ opacity: 1, scaleX: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.1 }}
              />
              <motion.p
                className="text-xl text-gray-600 leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                Find comprehensive answers to common questions about our vehicle history report system and services.
              </motion.p>
            </div>
          </AnimatedSection>

          {/* Right Column - FAQ Items */}
          <div className="relative lg:col-span-3">
            <motion.div
              className="bg-white rounded-2xl border border-gray-200 shadow-lg"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {/* Content */}
              <div className="p-6 md:p-8">
                <div className="space-y-6">
                  {faqData.map((item, index) => (
                    <motion.div
                      key={index}
                      className={`border-b border-gray-200/60 pb-6 last:border-b-0 last:pb-0 ${
                        openItems.includes(index) ? 'faq-open' : ''
                      }`}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                    >
                      <button
                        className="flex justify-between items-center w-full text-left focus:outline-none group"
                        onClick={() => toggleItem(index)}
                      >
                        <h3 className="text-lg font-semibold text-gray-900 pr-4 group-hover:text-primary transition-colors">
                          {item.question}
                        </h3>
                        <motion.span
                          className="text-primary ml-4 flex-shrink-0"
                          animate={{ rotate: openItems.includes(index) ? 45 : 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </motion.span>
                      </button>

                      <motion.div
                        initial={false}
                        animate={{
                          height: openItems.includes(index) ? "auto" : 0,
                          opacity: openItems.includes(index) ? 1 : 0,
                        }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="pt-4">
                          <p className="text-gray-600 leading-relaxed">
                            {item.answer}
                          </p>
                        </div>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Contact CTA */}
      <div className="text-center mt-12 relative z-10">
        <p className="text-gray-600 mb-6 text-lg">
          Can't find the answer you're looking for?
        </p>
        <Button
          className="inline-flex items-center px-8 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-lg text-base border border-primary/30 transition-colors"
          asChild
        >
          <a href="/contact">
            Contact Our Support Team
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ml-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </a>
        </Button>
      </div>
    </div>
  );
}
