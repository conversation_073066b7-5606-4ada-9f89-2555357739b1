'use client';

import { AnimatedSection } from '@/components/landing-page';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useEffect, useState } from 'react';

// Utility function to format numbers with K+ notation
const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return Math.floor(num / 1000) + 'K+';
  }
  return num.toString();
};

export function FeaturesSection() {
  const [vehicleCount, setVehicleCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVehicleCount = async () => {
      try {
        const { count } = await supabase
          .from('vehicles')
          .select('*', { count: 'exact', head: true });

        setVehicleCount(count || 0);
      } catch (error) {
        console.error('Error fetching vehicle count:', error);
        // Set default value for demo purposes (same as TrustedByGovCard)
        setVehicleCount(15420);
      } finally {
        setLoading(false);
      }
    };

    fetchVehicleCount();
  }, []);
  return (
    <div className="mb-20 relative">
      {/* Background decorative elements */}
      <motion.div
        className="absolute top-40 -left-20 w-40 h-40 rounded-full bg-primary/5 opacity-70"
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 15, 0],
        }}
        transition={{
          duration: 18,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute bottom-20 -right-20 w-32 h-32 rounded-full bg-primary/3 opacity-60"
        animate={{
          scale: [1, 1.3, 1],
          rotate: [0, -20, 0],
        }}
        transition={{
          duration: 22,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: 3,
        }}
      />

      {/* Header Section */}
      <AnimatedSection>
        <div className="text-center mb-16 relative z-10">
          <motion.h2
            className="text-4xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Complete Vehicle Intelligence Platform
          </motion.h2>
          <motion.div
            className="h-1 w-20 bg-gradient-to-r from-primary/60 via-primary to-primary/60 mx-auto mb-6 rounded-full"
            initial={{ opacity: 0, scaleX: 0 }}
            whileInView={{ opacity: 1, scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.1 }}
          />
          <motion.div
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <p className="text-xl text-gray-600">From comprehensive history reports to real-time alerts, discover how our platform transforms vehicle information management for individuals, businesses, and government agencies.</p>
          </motion.div>
        </div>
      </AnimatedSection>

      {/* Core Features Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <FeatureHighlight
          title="Instant Vehicle Intelligence"
          description="Get comprehensive vehicle history reports in seconds. Our AI-powered system analyzes multiple data sources to provide you with accurate, up-to-date information about any vehicle's past."
          features={[
            "Real-time data processing",
            "Multi-source verification",
            "Instant report generation",
            "Mobile-optimized access"
          ]}
          icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />}
          delay={0.1}
        />

        <FeatureHighlight
          title="Advanced Security & Compliance"
          description="Built with enterprise-grade security and full regulatory compliance. Your data is protected with bank-level encryption while maintaining transparency and auditability."
          features={[
            "End-to-end encryption",
            "GDPR & SOC 2 compliant",
            "Audit trail tracking",
            "Role-based access control"
          ]}
          icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />}
          delay={0.2}
        />
      </div>

      {/* Action Cards Section */}
      <AnimatedSection delay={0.3}>
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 md:p-12 relative overflow-hidden">
          <motion.div
            className="absolute top-0 right-0 w-40 h-40 bg-primary/5 rounded-full -translate-y-20 translate-x-20"
            animate={{
              scale: [1, 1.1, 1],
              rotate: [0, 10, 0],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
            }}
          />

          <div className="text-center mb-12 relative z-10">
            <motion.h3
              className="text-3xl font-bold text-gray-900 mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Take Action Today
            </motion.h3>
            <motion.p
              className="text-lg text-gray-600 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Whether you're buying a car, managing a fleet, or ensuring public safety, our platform has the tools you need.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 relative z-10">
            <ActionCard
              title="Search Vehicle History"
              description="Get instant access to comprehensive vehicle records and make informed decisions."
              icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />}
              buttonText="Start Search"
              buttonLink="/auth/signin?redirect=/dashboard/vehicles"
              accent="primary"
              delay={0.1}
            />

            <ActionCard
              title="Report Vehicle Issues"
              description="Help keep the community safe by reporting accidents, fraud, or suspicious activities."
              icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />}
              buttonText="Submit Alert"
              buttonLink="/auth/signin?redirect=/dashboard/alerts/new"
              accent="crimson"
              delay={0.2}
            />

            <ActionCard
              title="Access Dashboard"
              description="Manage your vehicle records, alerts, and account settings in one centralized location."
              icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />}
              buttonText="Open Dashboard"
              buttonLink="/auth/signin?redirect=/dashboard"
              accent="darkBlue"
              delay={0.3}
            />

            <ComingSoonCard
              title="Mobile App"
              description="Access vehicle records on-the-go with our upcoming mobile application for iOS and Android."
              icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />}
              delay={0.4}
            />
          </div>
        </div>
      </AnimatedSection>

      {/* Stats Section */}
      <AnimatedSection delay={0.4}>
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          <StatCard
            number={loading ? "..." : formatNumber(vehicleCount)}
            label="Vehicles Tracked"
            delay={0.1}
          />
          <StatCard number="99.9%" label="Uptime Guarantee" delay={0.2} />
          <StatCard number="24/7" label="Support Available" delay={0.3} />
          <StatCard number="5 Min" label="Max Response Time" delay={0.4} />
        </div>
      </AnimatedSection>
    </div>
  );
}

// Component interfaces
interface FeatureHighlightProps {
  title: string;
  description: string;
  features: string[];
  icon: React.ReactNode;
  delay?: number;
}

interface ActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  buttonText: string;
  buttonLink: string;
  accent: 'primary' | 'crimson' | 'darkBlue';
  delay?: number;
}

interface StatCardProps {
  number: string;
  label: string;
  delay?: number;
}

interface ComingSoonCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  delay?: number;
}

// FeatureHighlight Component
function FeatureHighlight({ title, description, features, icon, delay = 0 }: FeatureHighlightProps) {
  return (
    <AnimatedSection delay={delay}>
      <motion.div
        className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 h-full"
        whileHover={{
          y: -5,
          boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        }}
        transition={{ type: "spring", stiffness: 300, damping: 15 }}
      >
        <div className="flex items-center gap-4 mb-6">
          <motion.div
            className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {icon}
            </svg>
          </motion.div>
          <h3 className="text-2xl font-bold text-gray-900">{title}</h3>
        </div>

        <p className="text-gray-600 mb-6 leading-relaxed">{description}</p>

        <ul className="space-y-3">
          {features.map((feature, index) => (
            <motion.li
              key={index}
              className="flex items-center gap-3"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: delay + (index * 0.1) }}
            >
              <div className="w-2 h-2 rounded-full bg-primary"></div>
              <span className="text-gray-700">{feature}</span>
            </motion.li>
          ))}
        </ul>
      </motion.div>
    </AnimatedSection>
  );
}

// ActionCard Component
function ActionCard({ title, description, icon, buttonText, buttonLink, accent, delay = 0 }: ActionCardProps) {
  const accentColors = {
    primary: 'bg-primary hover:bg-primary/90 border-primary/30',
    crimson: 'bg-red-700/80 hover:bg-red-700/90 border-red-700/30',
    darkBlue: 'bg-slate-800 hover:bg-slate-900 border-slate-800/30'
  };

  return (
    <motion.div
      className="bg-white rounded-xl shadow-md p-6 h-full flex flex-col"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{
        y: -5,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      }}
    >
      <div className="flex items-center gap-3 mb-4">
        <motion.div
          className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center"
          whileHover={{ scale: 1.1, rotate: 10 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            {icon}
          </svg>
        </motion.div>
        <h4 className="text-lg font-semibold text-gray-900">{title}</h4>
      </div>

      <p className="text-gray-600 mb-6 flex-grow">{description}</p>

      <Link href={buttonLink}>
        <Button className={`w-full ${accentColors[accent]} text-white font-medium transition-all`}>
          {buttonText}
        </Button>
      </Link>
    </motion.div>
  );
}

// StatCard Component
function StatCard({ number, label, delay = 0 }: StatCardProps) {
  return (
    <motion.div
      className="text-center"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
    >
      <motion.div
        className="text-3xl md:text-4xl font-bold text-primary mb-2"
        initial={{ scale: 0 }}
        whileInView={{ scale: 1 }}
        viewport={{ once: true }}
        transition={{ type: "spring", stiffness: 300, damping: 15, delay: delay + 0.2 }}
      >
        {number}
      </motion.div>
      <div className="text-gray-600 font-medium">{label}</div>
    </motion.div>
  );
}

// ComingSoonCard Component
function ComingSoonCard({ title, description, icon, delay = 0 }: ComingSoonCardProps) {
  return (
    <motion.div
      className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl shadow-md p-6 h-full flex flex-col border-2 border-dashed border-gray-300 relative overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{
        y: -3,
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      }}
    >
      {/* Coming Soon Badge */}
      <motion.div
        className="absolute top-3 right-3 bg-primary/10 text-primary text-xs font-semibold px-2 py-1 rounded-full border border-primary/20"
        initial={{ scale: 0, rotate: -10 }}
        whileInView={{ scale: 1, rotate: 0 }}
        viewport={{ once: true }}
        transition={{ type: "spring", stiffness: 400, damping: 10, delay: delay + 0.2 }}
      >
        Coming Soon
      </motion.div>

      <div className="flex items-center gap-3 mb-4">
        <motion.div
          className="w-10 h-10 rounded-lg bg-gray-200 flex items-center justify-center"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            {icon}
          </svg>
        </motion.div>
        <h4 className="text-lg font-semibold text-gray-700">{title}</h4>
      </div>

      <p className="text-gray-500 mb-6 flex-grow">{description}</p>

      <div className="w-full py-3 bg-gray-200 text-gray-500 font-medium rounded-lg text-center cursor-not-allowed">
        Notify Me
      </div>
    </motion.div>
  );
}
