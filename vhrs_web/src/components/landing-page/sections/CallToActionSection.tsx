'use client';

import { AnimatedSection } from '@/components/landing-page';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import Link from 'next/link';

export function CallToActionSection() {
  return (
    <div className="mt-16 mb-12">
      <AnimatedSection>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <ActionCard
            title="Request Vehicle History"
            description="Search for a vehicle by VIN, plate number, or chassis number to request its history record."
            icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />}
            buttonText="Get Started"
            buttonLink="/auth/signin?redirect=/dashboard/vehicles"
          />

          <ActionCard
            title="Submit an Alert"
            description="Report accidents, fraud, or any suspicious vehicle-related activity."
            icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />}
            buttonText="Submit Alert"
            buttonLink="/auth/signin?redirect=/dashboard/alerts/new"
          />

          <ActionCard
            title="View Your Records"
            description="Access your previously requested vehicle history reports and submitted alerts."
            icon={<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />}
            buttonText="View Records"
            buttonLink="/auth/signin?redirect=/dashboard"
          />
        </div>
      </AnimatedSection>
    </div>
  );
}

interface ActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  buttonText: string;
  buttonLink: string;
}

function ActionCard({ title, description, icon, buttonText, buttonLink }: ActionCardProps) {
  return (
    <motion.div
      className="bg-white overflow-hidden shadow rounded-lg"
      whileHover={{
        y: -5,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      }}
      transition={{ type: "spring", stiffness: 300, damping: 15 }}
    >
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center gap-3 mb-3">
          <motion.div
            whileHover={{ rotate: 15, scale: 1.2 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {icon}
            </svg>
          </motion.div>
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
        <p className="mt-2 text-sm text-gray-500">
          {description}
        </p>
        <div className="mt-4">
          <Link href={buttonLink}>
            <Button
              className="w-full relative overflow-hidden group"
            >
              <span className="relative z-10">{buttonText}</span>
              <span className="absolute inset-0 bg-primary/80 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
            </Button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
