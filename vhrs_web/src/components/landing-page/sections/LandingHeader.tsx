'use client';

import { Button } from '@/components/ui/button';
import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { motion } from 'framer-motion';
import Link from 'next/link';

export function LandingHeader() {
  const { settings } = useSystemSettings();

  return (
    <motion.header
      className="bg-white shadow-sm sticky top-0 z-50"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
        <Link href="/">
          <motion.h1
            className="text-2xl font-bold text-gray-900 cursor-pointer"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {settings.system_name}
          </motion.h1>
        </Link>
        <div className="flex space-x-4">
          <Link href="/auth/signin">
            <Button
              variant="default"
              className="relative overflow-hidden group"
            >
              <span className="relative z-10">Sign In</span>
              <span className="absolute inset-0 bg-primary/80 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
            </Button>
          </Link>
          <Link href="/auth/signup">
            <Button
              variant="outline"
              className="relative overflow-hidden group border-primary/50 hover:border-primary"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-300">Sign Up</span>
              <span className="absolute inset-0 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
            </Button>
          </Link>
        </div>
      </div>
    </motion.header>
  );
}
