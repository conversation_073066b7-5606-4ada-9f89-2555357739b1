'use client';

import { LandingContactInfo } from '@/components/landing-page';
import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { motion } from 'framer-motion';
import Link from 'next/link';

export function LandingFooter() {
  const { settings } = useSystemSettings();

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Background decorative elements */}
      <motion.div
        className="absolute top-0 right-0 w-96 h-96 rounded-full bg-primary/5 opacity-10"
        animate={{
          scale: [1, 1.2, 1],
          x: [0, 20, 0],
          y: [0, -20, 0],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute bottom-0 left-0 w-64 h-64 rounded-full bg-primary/5 opacity-10"
        animate={{
          scale: [1, 1.3, 1],
          x: [0, -10, 0],
          y: [0, 10, 0],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: 2,
        }}
      />

      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          <div className="col-span-1 md:col-span-2">
            <motion.h3
              className="text-xl font-bold mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              {settings.system_name}
            </motion.h3>
            <motion.p
              className="text-gray-300 mb-6 max-w-md"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Providing comprehensive vehicle history records to help you make informed decisions about vehicle purchases and ownership in Uganda.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <LandingContactInfo />
            </motion.div>
          </div>

          <FooterLinkSection
            title="Quick Links"
            links={[
              { text: "Sign In", href: "/auth/signin" },
              { text: "Create Account", href: "/auth/signup" },
              { text: "Features", href: "#learn-more" }
            ]}
            delay={0.3}
          />

          <FooterLinkSection
            title="Resources"
            links={[
              { text: "Contact Us", href: "/contact" },
              { text: "Privacy Policy", href: "/privacy" },
              { text: "Terms of Service", href: "/terms" }
            ]}
            delay={0.4}
          />
        </div>

        <motion.div
          className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} {settings.system_name}. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <SocialIcon href="#" ariaLabel="Facebook">
              <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
            </SocialIcon>
            <SocialIcon href="#" ariaLabel="Twitter">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </SocialIcon>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}

interface FooterLinkSectionProps {
  title: string;
  links: { text: string; href: string }[];
  delay?: number;
}

function FooterLinkSection({ title, links, delay = 0 }: FooterLinkSectionProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
    >
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <ul className="space-y-3">
        {links.map((link, index) => (
          <li key={index}>
            <Link href={link.href} className="text-gray-300 hover:text-white transition-colors relative group">
              {link.text}
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
            </Link>
          </li>
        ))}
      </ul>
    </motion.div>
  );
}

interface SocialIconProps {
  href: string;
  ariaLabel: string;
  children: React.ReactNode;
}

function SocialIcon({ href, ariaLabel, children }: SocialIconProps) {
  return (
    <motion.a
      href={href}
      className="text-gray-400 hover:text-white transition-colors"
      whileHover={{ scale: 1.2, color: "#4f46e5" }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <span className="sr-only">{ariaLabel}</span>
      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        {children}
      </svg>
    </motion.a>
  );
}
