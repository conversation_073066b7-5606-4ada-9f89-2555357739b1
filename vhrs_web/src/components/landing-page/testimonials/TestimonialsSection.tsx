'use client';

import { AnimatedSection } from '@/components/landing-page/animations';
import { TestimonialCard } from '@/components/landing-page/testimonials/TestimonialCard';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/lib/supabase';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface Testimonial {
  id: string;
  full_name: string;
  context: string;
  experience: string;
  created_at: string;
}

export function TestimonialsSection() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTestimonials() {
      try {
        setLoading(true);

        const { data, error } = await supabase.rpc('get_featured_testimonials');

        if (error) {
          console.error('Error fetching testimonials from RPC:', error);
          throw error;
        }
        setTestimonials(data || []);
      } catch (err) {
        console.error('Error fetching testimonials:', err);
        setError('Failed to load testimonials. Please try again later.');
        setTestimonials([]);
      } finally {
        setLoading(false);
      }
    }

    fetchTestimonials();
  }, []);

  // Render loading skeletons
  if (loading) {
    return (
      <div className="mt-20 mb-24 relative overflow-hidden px-4 py-4 sm:px-6 lg:px-6">
        <AnimatedSection>
          <div className="text-center mb-16 relative z-10">
            <motion.h2
              className="text-3xl font-bold text-gray-900 mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              What Our Users Say
            </motion.h2>
            <motion.div
              className="h-1 w-20 bg-gradient-to-r from-primary/60 via-primary to-primary/60 mx-auto mb-6 rounded-full"
              initial={{ opacity: 0, scaleX: 0 }}
              whileInView={{ opacity: 1, scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.1 }}
            />
            <motion.div
              className="max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <p className="text-xl text-gray-600">Discover how our platform has helped users make informed decisions and avoid potential issues.</p>
            </motion.div>
          </div>
        </AnimatedSection>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white rounded-xl shadow-md p-6 relative h-full">
              <Skeleton className="h-4 w-4/5 mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4 mb-6" />
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Skeleton className="h-4 w-1/3 mb-2" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // If we have no testimonials and no error, hide the section completely
  if (testimonials.length === 0 && !error) {
    return null;
  }

  return (
    <div className="mt-20 mb-24 relative overflow-hidden px-4 py-4 sm:px-6 lg:px-6">
      {/* Background decorative elements */}
      <motion.div
        className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-primary/5"
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, 10, 0],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute -bottom-32 -left-32 w-96 h-96 rounded-full bg-primary/3"
        animate={{
          scale: [1, 1.15, 1],
          rotate: [0, -10, 0],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: 2,
        }}
      />

      <AnimatedSection>
        <div className="text-center mb-16 relative z-10">
          <motion.h2
            className="text-3xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            What Our Users Say
          </motion.h2>
          <motion.div
            className="h-1 w-20 bg-gradient-to-r from-primary/60 via-primary to-primary/60 mx-auto mb-6 rounded-full"
            initial={{ opacity: 0, scaleX: 0 }}
            whileInView={{ opacity: 1, scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.1 }}
          />
          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <p className="text-xl text-gray-600">Discover how our platform has helped users make informed decisions and avoid potential issues.</p>
          </motion.div>
        </div>
      </AnimatedSection>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10">
        {testimonials.map((testimonial, index) => (
          <TestimonialCard
            key={testimonial.id}
            name={testimonial.full_name}
            location={testimonial.context}
            content={testimonial.experience}
            delay={0.1 * (index + 1)}
          />
        ))}
      </div>
    </div>
  );
}
