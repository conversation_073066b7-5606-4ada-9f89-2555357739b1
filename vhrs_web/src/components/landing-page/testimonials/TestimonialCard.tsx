'use client';

import { motion } from 'framer-motion';

interface TestimonialCardProps {
  name: string;
  location: string;
  content: string;
  delay?: number;
}

export function TestimonialCard({
  name,
  location,
  content,
  delay = 0,
}: TestimonialCardProps) {

  return (
    <motion.div
      className="bg-white rounded-xl shadow-md p-6 relative h-full flex flex-col"
      whileHover={{
        y: -10,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      }}
      transition={{ type: "spring", stiffness: 300, damping: 15 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
    >
      <motion.div
        className="absolute top-0 right-0 -mt-4 -mr-4 bg-primary/10 rounded-full p-3"
        whileHover={{
          rotate: 15,
          backgroundColor: "rgba(var(--color-primary), 0.2)",
        }}
        transition={{ type: "spring", stiffness: 300, damping: 10 }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </motion.div>

      {/* Experience message at the top */}
      <p className="text-gray-600 italic flex-grow">{content}</p>

      {/* Divider */}
      <div className="border-t border-gray-100 my-4"></div>

      {/* Name and context at the bottom */}
      <div className="mt-auto">
        <p className="font-semibold text-gray-900">{name}</p>
        <p className="text-sm text-gray-500">{location}</p>
      </div>
    </motion.div>
  );
}
