'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import { CheckCircle, RefreshCw, Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

type Permission = {
  id: string;
  code: string;
  description: string;
};

type Role = {
  id: string;
  name: string;
  description: string | null;
};

type RolePermission = {
  role_id: string;
  permission_id: string;
};

export function PermissionManager() {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>([]);
  const [loading, setLoading] = useState(true);
  const [generatingPermissions, setGeneratingPermissions] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('admin');
  const [savingChanges, setSavingChanges] = useState(false);

  // Fetch permissions, roles, and role permissions
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch permissions
      const { data: permissionsData, error: permissionsError } = await supabase
        .from('permissions')
        .select('*')
        .order('code');

      if (permissionsError) throw permissionsError;

      // Fetch roles
      const { data: rolesData, error: rolesError } = await supabase
        .from('roles')
        .select('*')
        .order('name');

      if (rolesError) throw rolesError;

      // Fetch role permissions
      const { data: rolePermissionsData, error: rolePermissionsError } = await supabase
        .from('role_permissions')
        .select('*');

      if (rolePermissionsError) throw rolePermissionsError;

      setPermissions(permissionsData || []);
      setRoles(rolesData || []);
      setRolePermissions(rolePermissionsData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load permissions data');
    } finally {
      setLoading(false);
    }
  };

  // Generate permissions
  const handleGeneratePermissions = async () => {
    setGeneratingPermissions(true);
    try {
      const { data, error } = await supabase.rpc('generate_system_permissions');

      if (error) throw error;

      if (data.success) {
        toast.success(data.message);
        // Refresh the data
        fetchData();
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('Error generating permissions:', error);
      toast.error('Failed to generate permissions');
    } finally {
      setGeneratingPermissions(false);
    }
  };

  // Check if a role has a permission
  const hasPermission = (roleId: string, permissionId: string) => {
    return rolePermissions.some(
      (rp) => rp.role_id === roleId && rp.permission_id === permissionId
    );
  };

  // Toggle a permission for a role
  const togglePermission = async (roleId: string, permissionId: string, hasPermission: boolean) => {
    setSavingChanges(true);
    try {
      if (hasPermission) {
        // Remove the permission
        const { error } = await supabase
          .from('role_permissions')
          .delete()
          .eq('role_id', roleId)
          .eq('permission_id', permissionId);

        if (error) throw error;

        setRolePermissions(rolePermissions.filter(
          (rp) => !(rp.role_id === roleId && rp.permission_id === permissionId)
        ));
      } else {
        // Add the permission
        const { error } = await supabase
          .from('role_permissions')
          .insert([{ role_id: roleId, permission_id: permissionId }]);

        if (error) throw error;

        setRolePermissions([...rolePermissions, { role_id: roleId, permission_id: permissionId }]);
      }
      toast.success('Permission updated successfully');
    } catch (error) {
      console.error('Error updating permission:', error);
      toast.error('Failed to update permission');
    } finally {
      setSavingChanges(false);
    }
  };

  // Filter permissions based on search term
  const filteredPermissions = permissions.filter((permission) => {
    return (
      permission.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Group permissions by category (table name)
  const groupedPermissions = filteredPermissions.reduce((groups, permission) => {
    const category = permission.code.split(':')[0];
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(permission);
    return groups;
  }, {} as Record<string, Permission[]>);

  // Get the active role
  const activeRole = roles.find((role) => role.name === activeTab);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Permission Management</CardTitle>
        <CardDescription>
          Manage system permissions and role assignments. Permissions are automatically synchronized with the database schema.
        </CardDescription>
        <div className="mt-4 flex items-center text-sm text-green-600">
          <CheckCircle className="mr-2 h-4 w-4" />
          <span>Permissions are automatically generated based on database tables and updated when tables are created or deleted</span>
        </div>
        <span className='mt-0 text-sm text-neutral-500'>To manually sync permissions with the database schema, click the <b>Sync Permissions</b> button below.</span>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-neutral-500" />
            <Input
              type="search"
              placeholder="Search permissions..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button
            onClick={handleGeneratePermissions}
            disabled={generatingPermissions}
            className="flex items-center gap-2"
          >
            {generatingPermissions ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Syncing...</span>
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4" />
                <span>Sync Permissions</span>
              </>
            )}
          </Button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-500"></div>
          </div>
        ) : (
          <>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${roles.length}, minmax(0, 1fr))` }}>
                {roles.map((role) => (
                  <TabsTrigger key={role.id} value={role.name}>
                    {role.name.charAt(0).toUpperCase() + role.name.slice(1)}
                  </TabsTrigger>
                ))}
              </TabsList>

              {roles.map((role) => (
                <TabsContent key={role.id} value={role.name} className="mt-4">
                  <div className="space-y-6">
                    {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                      <div key={category} className="space-y-2">
                        <h3 className="text-lg font-medium capitalize">{category}</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {categoryPermissions.map((permission) => (
                            <div
                              key={permission.id}
                              className="flex items-center space-x-2 border rounded-md p-2"
                            >
                              <Checkbox
                                id={`${role.id}-${permission.id}`}
                                checked={hasPermission(role.id, permission.id)}
                                onCheckedChange={(checked) => {
                                  togglePermission(role.id, permission.id, !!checked);
                                }}
                                disabled={savingChanges}
                              />
                              <Label
                                htmlFor={`${role.id}-${permission.id}`}
                                className="flex-1 cursor-pointer"
                              >
                                <div className="font-medium">{permission.code}</div>
                                <div className="text-sm text-neutral-500">{permission.description}</div>
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-neutral-500">
          {filteredPermissions.length} permissions found
        </div>
        <Button variant="outline" onClick={fetchData} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </Button>
      </CardFooter>
    </Card>
  );
}
