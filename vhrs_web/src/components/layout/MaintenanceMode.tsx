'use client';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useSystemSettings } from '@/contexts/SystemSettingsContext';
import { AlertTriangle } from 'lucide-react';
import { useEffect } from 'react';
import { toast } from 'sonner';

export function MaintenanceMode() {
  const { settings, loading } = useSystemSettings();

  // Show a toast notification when maintenance mode is active
  useEffect(() => {
    if (!loading && settings.maintenance_mode) {
      toast.warning('System is currently in maintenance mode. Some features may be unavailable.');
    }
  }, [loading, settings.maintenance_mode]);

  // If not in maintenance mode or still loading, don't show anything
  if (loading || !settings.maintenance_mode) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-6">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>System Maintenance</AlertTitle>
      <AlertDescription>
        {settings.maintenance_message || 'The system is currently undergoing maintenance. Some features may be unavailable.'}
      </AlertDescription>
    </Alert>
  );
}
