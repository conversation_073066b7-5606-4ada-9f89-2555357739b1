# Mobile Functional Requirements

Author: <PERSON><PERSON>
Status: Draft
Category: PRD
Last edited time: April 28, 2025 11:51 PM

# 📄 **Mobile App Functional Requirements**

---

## **User Roles**

- **Standard User**

---

## **Functional Requirements Overview**

- Sign Up
- Log In
- Update Profile
- Set Notification Preferences
- Request Vehicle History Record
- View Past Requested Records
- Track Status of Requested Records
- Submit Vehicle Alerts (Accident, Fraud, etc.)
- View Own Submitted Alerts
- Track Status of Submitted Alerts
- Receive Push Notifications
- Log Out
- Delete Account

---

## **Functional Requirements Descriptions**

---

### **1. Sign Up**

- Standard Users can create an account using their phone number (primary identifier)
- Users can also sign up with  their email address with a password
- Users can also sign up with google as well as apple sign in.
- After creating the account, the system a form to capture the user's full name, phone number, and optionally email and notification preferences.

### **2. Log In**

- Users can log in using their phone number with an OTP
- Users can log in with email and password
- Users can log in with Google or Apple login

### **3. Update Profile**

- Users can update their personal details such as full name, email address or phone number, and notification preferences.
- Once email or phone number are updated, the Supabase auth should also be updated.

### **4. Set Notification Preferences**

- Users can configure whether to receive push notifications or SMS alerts for important updates.
- Preferences are stored in a `preferences` JSONB object.

### **5. Request Vehicle History Record**

- Users can request a **Vehicle History Record** by entering a **VIN**, **License Plate**, or **Chassis Number**.
- If the vehicle is not found, the system immediately notifies the user.

### **6. View Past Requested Records**

- Users can view a list of all their previously requested vehicle history records, along with their current statuses (e.g., Ready, Processing).

### **7. Track Status of Requested Records**

- Users can track the status of each requested history record through stages like "Processing," "Ready," or "Failed."

### **8. Submit Vehicle Alerts**

- Users can submit an alert concerning a specific vehicle, such as reports of accidents, mileage fraud, or theft.
- Submissions can include vehicle identification and optional supporting evidence (e.g., images).

### **9. View Own Submitted Alerts**

- Users can view a history of the alerts they have submitted, including details such as vehicle information, alert type, and submission date.

### **10. Track Status of Submitted Alerts**

- Users can track the review status of their submitted alerts (e.g., "Under Review," "Verified," "Dismissed").

### **11. Receive Push Notifications**

- Push notifications (if enabled) inform users of important actions, including:
  - Vehicle History Record ready
  - Alert status updates
  - Critical account activities

### **12. Log Out**

- Users can securely log out of the application at any time.

### **13. Delete Account**

- Users may permanently delete their account, triggering data removal or anonymization according to system policies.

---

# 📋 **Functional Requirements Table**

| ID | Role | Functional Requirement | Description |
| --- | --- | --- | --- |
| FR1 | Standard User | Sign Up | Create an account using a phone number and optional email. |
| FR2 | Standard User | Log In | Access the app using phone/email and password or OTP. |
| FR3 | Standard User | Update Profile | Edit user details such as name, email, and preferences. |
| FR4 | Standard User | Set Notification Preferences | Configure preferences for receiving push or SMS notifications. |
| FR5 | Standard User | Request Vehicle History Record | Request a vehicle history record by VIN, Plate, or Chassis Number. |
| FR6 | Standard User | View Past Requested Records | View a list of previously requested vehicle history records. |
| FR7 | Standard User | Track Status of Requested Records | Monitor the progress of requested vehicle history records. |
| FR8 | Standard User | Submit Vehicle Alerts | Submit alerts for incidents like accidents or fraud. |
| FR9 | Standard User | View Own Submitted Alerts | View submitted alerts and related details. |
| FR10 | Standard User | Track Status of Submitted Alerts | Track the investigation status of submitted vehicle alerts. |
| FR11 | Standard User | Receive Push Notifications | Receive important system notifications via mobile alerts. |
| FR12 | Standard User | Log Out | Log out securely from the mobile application. |
| FR13 | Standard User | Delete Account | Permanently delete or anonymize the user account. |
