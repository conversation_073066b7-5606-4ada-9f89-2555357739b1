# Mobile Architecture

Author: <PERSON><PERSON>
Status: Draft
Category: PRD
Last edited time: April 25, 2025 5:29 PM

## 📱 VHRS Mobile App Architecture

### 1. 🛠 Technology Stack

| Layer | Technology |
| --- | --- |
| **Frontend** | Flutter (Dart 3), Material 3, Responsive Design |
| **State Management** | BLoC (flutter_bloc), hydrated_bloc for persistence |
| **Navigation** | auto_route (declarative routing) |
| **Backend & Auth** | Supabase (PostgreSQL, Auth, Storage, Functions) |
| **Image Handling** | image_picker, image_cropper |
| **HTTP Client** | dio |
| **Storage** | Supabase Storage |
| **Analytics** | Audit trails stored in the database |
| **Location** | geolocator, geocoding |
| **Maps** | Google Maps or Mapbox SDKs |
| **Notifications** | Firebase Cloud Messaging (FCM) |

---

### 2. 🧱 Application Structure

Adopting a **Clean Architecture** approach with a **Feature-First** folder structure:

```
vbnet
CopyEdit
lib/
├── features/
│   ├── auth/
│   ├── vehicle_search/
│   ├── vehicle_details/
│   ├── profile/
│   ├── admin/
│   └── settings/
├── core/
│   ├── services/
│   ├── models/
│   ├── repositories/
│   ├── utils/
│   └── constants/
├── shared/
│   ├── widgets/
│   └── themes/
└── main.dart

```

- **Features**: Encapsulate UI, BLoC/Cubit, and data access for each feature.
- **Core**: Contains shared services, models, and utilities.
- **Shared**: Reusable widgets and theming.

---

### 3. 🔄 State Management

Utilizing the **BLoC** pattern for predictable state management

- **flutter_bloc**: Manages state transitions.
- **hydrated_bloc**: Persists state across app restarts.

Each feature has its own BLoC/Cubit to handle business logic and state.

---

### 4. 🔐 Authentication & Authorization

- **Supabase Auth**: Handles email/password authentication.
- **Role-Based Access Control (RBAC)**: Implemented via Supabase's RLS policies and the `roles`, `permissions`, `role_permissions`, and `user_roles` tables.
- **Session Management**: Managed by `supabase_flutter` package.

---

### 5. 📦 Data Layer

- **Repositories**: Abstract data access, interfacing with Supabase services.
- **Supabase Integration**: Utilizes `supabase_flutter` for database, storage, and function interactions.
- **Offline Support**: Plans to integrate offline-first capabilities using packages like Brick or Drift

---

### 6. 📲 Main Features & Screens

- **Authentication Flow**:
  - Onboarding
  - Login & Signup
  - Password Reset
- **Vehicle Search & History**:
  - Home Screen (search by VIN or Plate)
  - Search Results
  - Vehicle Summary
  - Ownership History
  - Accident Records
  - Maintenance Records
  - Report Download (PDF)
- **User Profile**:
  - View & Edit Profile
  - Change Password
  - Saved Reports
  - Logout
  - Delete Account
- **Admin Panel**:
  - User Management
  - View Audit Trails
  - Manage Vehicle Data
- **Settings & Support**:
  - Notification Settings
  - Report Issue / Contact Support

---

### 7. 📍 Navigation Structure

Managed by `auto_route` for declarative and nested routing:

```
bash
CopyEdit

/onboarding
/login
/home
  ├── /search
  ├── /report/:id
/profile
/admin
/settings

```

---

### 8. 🛡️ Security & Audit

- **Row-Level Security (RLS)**: Enforced on all Supabase tables to ensure data privacy.
- **Audit Trails**: All data changes are logged in the `data_submissions` table, capturing user actions like logins, searches, and report views.
- **Permissions**: Granular permissions managed via the `permissions` and `role_permissions` tables.

---

### 9. 🧪 Testing & CI/CD

- **Testing**:
  - Unit Tests: For business logic.
  - Widget Tests: For UI components.
  - Integration Tests: For end-to-end scenarios.
- **CI/CD**:
  - GitHub Actions: Automates build, test, and deployment processes.
  - Firebase App Distribution or TestFlight: For distributing test builds.

---

### 10. 📈 Analytics & Monitoring

- **Audit Logs**: Stored in the `data_submissions` table for internal analytics.
- **Error Monitoring**: Integration with services like Sentry for real-time error tracking.
- **Performance Monitoring**: Utilizing tools like Firebase Performance Monitoring.
