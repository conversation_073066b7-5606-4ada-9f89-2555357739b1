# Vehicle History Record System - Supabase Database

This directory contains the Supabase configuration and database schema for the Vehicle History Record System (VHRS). The database is designed to track comprehensive vehicle history including ownership, maintenance, accidents, and fraud detection.

## Directory Structure

- `config.toml` - Supabase configuration file
- `schema.sql` - Main database schema file
- `migrations/` - Database migration files containing schema changes and updates
- `functions/` - Edge Functions
  - `generate_vehicle_history/` - Function to generate comprehensive vehicle history records based on vehicle ID

## Database Schema Overview

The database schema includes the following main components:

### User Management

- `profiles` - User profiles with contact information
- `roles` - User roles (admin, user, and custom roles created by admins)
- `permissions` - Granular permissions for actions
- `role_permissions` - Mapping between roles and permissions
- `user_roles` - Mapping between users and roles

When a user signs up through Supabase Auth, a database trigger automatically creates an entry in the `profiles` table and assigns the default 'user' role. If you need to manually sync users, you can use the `sync_auth_users()` SQL function or the `sync-users.js` script (see Troubleshooting section).

### Permission System

The VHRS uses an automated permission system that generates permissions based on database tables:

#### Permission Structure

Permissions follow the format `table:action` (e.g., `vehicle:create`, `alert:view`). For each table, four standard actions are available:

- `create` - Add new records to the table
- `view` - View records in the table
- `edit` - Modify existing records in the table
- `delete` - Delete records from the table

#### Special Permissions

In addition to table-based permissions, the system includes special permissions:

- `user:manage` - Manage users and roles (system)
- `audit:view` - View audit logs (system)

#### Automatic Permission Generation

Permissions are automatically generated and maintained through:

1. **Event Triggers**: When tables are created or dropped, permissions are automatically updated
2. **Excluded Tables**: System tables and tables managed through special permissions are excluded from automatic permission generation
3. **Default Role Assignments**: Permissions are assigned to core roles based on predefined rules:
   - **Admin**: All permissions
   - **User**: View permissions for vehicle, alert, history, and mileage records

   Additional roles (like data-agent) can be created and managed by administrators through the UI

#### Permission Management

Administrators can manage permissions through the Settings page in the application, which allows:

1. Viewing all available permissions
2. Assigning permissions to roles
3. Manually syncing permissions with the database schema

### Vehicle Data

- `vehicles` - Core vehicle information (VIN, make, model, year)
- `vehicle_license_plates` - License plate history with date ranges
- `vehicle_colors` - Vehicle color history
- `ownership_history` - Vehicle ownership records
- `accidents` - Accident records
- `maintenance_records` - Service and maintenance history
- `mileage_records` - Odometer readings with fraud detection
- `alerts` - Alerts by users over crime and suspicious activities
- `alert_media` - Media attachments for alerts
- `history_report_requests` - Vehicle history report requests

### System Operations

- `data_submissions` - Audit trail for data changes

## Getting Started

### Prerequisites

- [Supabase CLI](https://supabase.com/docs/guides/cli)
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [Node.js](https://nodejs.org/) (for Realtime processor service)

### Local Development

1. **Start Docker Desktop**

2. **Start Local Supabase**

   ```bash
   cd vhrs_data
   supabase start
   ```

3. **Reset Database (Apply Schema)**

   ```bash
   supabase db reset
   ```

4. **Access Supabase Studio**
   Open [http://127.0.0.1:54323](http://127.0.0.1:54323) in your browser

5. **Start the Realtime Processor Service**

   ```bash
   cd ../services
   ./start-realtime-processor.sh
   ```

   This service listens for new history record requests and processes them using the Edge Function.

### Database Migrations

After making schema changes like renaming tables or columns, you should create and apply migrations:

1. **Create a New Migration**

   ```bash
   supabase migration new migration_name
   ```

   **Naming Convention:**
   - Supabase automatically adds a timestamp prefix (e.g., `YYYYMMDDHHMMSS_`)
   - Use descriptive names with action prefixes: `add_`, `update_`, `remove_`, `fix_`
   - Include affected table(s): `add_details_to_alert_media`
   - Use snake_case: `create_user_preferences_table`
   - Example: `YYYYMMDDHHMMSS_add_details_to_alert_media.sql`

2. **Apply Migrations**

   ```bash
   # Apply all migrations (resets the database)
   supabase db reset
   ```

   This will reset the database and apply all migrations in order, including your new one.

   To apply a specific migration without resetting the database:

   ```bash
   # Apply a specific migration
   supabase migration apply YYYYMMDDHHMMSS

   # Or using npx
   npx supabase migration apply YYYYMMDDHHMMSS
   ```

   This is useful when you want to apply a single migration without affecting the rest of the database.

3. **Creating Migrations with the Helper Script**

   You can use the helper script to create migrations:

   ```bash
   ./supabase.sh new-migration add_field_to_table
   ```

   The timestamp will be automatically added by Supabase.

### Deploying to Supabase Cloud

1. **Login to Supabase**

   ```bash
   supabase login
   ```

2. **Link to Your Supabase Project**

   ```bash
   supabase link --project-ref your-project-ref
   ```

3. **Push Schema to Supabase Cloud**

   ```bash
   supabase db push
   ```

## Database Diagram

For a visual representation of the database schema, you can generate an ERD (Entity Relationship Diagram) using Supabase Studio, DBeaver or  [dbdiagram.io](https://dbdiagram.io) or many others.

## Extensions

The database uses the following PostgreSQL extensions:

- `postgis` - For location data (GEOMETRY type)
- `btree_gist` - For exclusion constraints on date ranges

## Troubleshooting

- **GIST Index Error**: If you encounter an error with GIST indexes, make sure the btree_gist extension is enabled
- **Docker Issues**: Ensure Docker Desktop is running before starting Supabase
- **Migration Conflicts**: If migrations conflict, you may need to reset the database
- **Auth Hook Issues**: If users are created in auth but not in the profiles table, run the sync script:

  You can sync users using one of these methods:

  1. Using the SQL function (recommended):

     ```sql
     -- Run this in the SQL editor to sync all users
     SELECT sync_auth_users();
     ```

  2. Using the JavaScript utility:

     ```bash
     # Using the sync-users.js script
     node sync-users.js
     ```

  These methods find users in auth.users that don't have corresponding entries in the profiles table and creates them with the default 'user' role.

  If you encounter an error about the functions not existing, make sure you've applied all migrations:

  ```bash
  supabase db reset
  ```

  The `sync_auth_users` function is defined in one of the migration files.

- **Permission Issues**: If permissions are not being generated correctly, you can manually sync them:

  ```sql
  -- Run this in the SQL editor to sync permissions with the database schema
  SELECT sync_permissions_with_schema();
  ```

  This will scan all tables in the database and generate appropriate permissions for them, then assign default permissions to roles.

## Edge Functions

The VHRS system uses Supabase Edge Functions to handle various backend operations. These serverless functions run on Deno and can be invoked via HTTP requests or triggered by database events.

For detailed documentation on each function, see the [functions/README.md](functions/README.md) file.

### User Creation and Role Assignment

The system uses a database trigger to handle user creation and role assignment when a new user signs up through Supabase Auth. The trigger automatically:

1. Creates an entry in the `profiles` table with the user's information
2. Assigns the default 'user' role to the new user

If you need to manually sync users (for example, if some users were created before the trigger was implemented), you can use the `sync_auth_users()` SQL function or the `sync-users.js` script.

### Working with Edge Functions

1. **Serve Functions Locally:**

   ```bash
   # Serve all functions
   supabase functions serve

   # Serve a specific function
   supabase functions serve generate_vehicle_history
   ```

2. **Deploy Functions:**

   ```bash
   # Deploy all functions
   supabase functions deploy

   # Deploy a specific function
   supabase functions deploy generate_vehicle_history
   ```

3. **Invoke Functions:**

   ```bash
   # Local development
   curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/generate_vehicle_history' \
     --header 'Authorization: Bearer <your-anon-key>' \
     --header 'Content-Type: application/json' \
     --data '{"vehicle_id":"<vehicle-id>"}'
   ```

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [PostGIS Documentation](https://postgis.net/documentation/)
