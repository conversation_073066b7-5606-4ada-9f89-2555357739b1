-- Create testimonials table
CREATE TABLE testimonials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL, -- Optional link to user
  full_name TEXT NOT NULL, -- Name to display
  context TEXT, -- e.g., "Car Buyer - Kampala, <PERSON><PERSON><PERSON>, <PERSON><PERSON>"
  experience TEXT NOT NULL, -- The testimonial text
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  is_featured BOOLEAN DEFAULT false, -- Whether to feature on landing page
  created_at TIMESTAMPTZ DEFAULT NOW(),
  approved_at TIMESTAMPTZ, -- When the testimonial was approved
  approved_by UUID REFERENCES profiles(id) -- Who approved the testimonial
);

-- <PERSON>reate indexes
CREATE INDEX idx_testimonials_status ON testimonials(status);
CREATE INDEX idx_testimonials_featured ON testimonials(is_featured) WHERE is_featured = true;
CREATE INDEX idx_testimonials_user ON testimonials(user_id);

-- Add RLS policies
-- ALTER TABLE testimonials ENABLE ROW LEVEL SECURITY;

-- Anyone can read approved testimonials
CREATE POLICY testimonials_read_policy ON testimonials
  FOR SELECT USING (status = 'approved');

-- Users can create their own testimonials
CREATE POLICY testimonials_insert_policy ON testimonials
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Users can update their own testimonials if not yet approved
CREATE POLICY testimonials_update_policy ON testimonials
  FOR UPDATE USING (
    user_id = auth.uid() AND status = 'pending'
  );

-- Only admins can approve/reject testimonials or mark as featured
CREATE POLICY testimonials_admin_policy ON testimonials
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = auth.uid() AND r.name = 'admin'
    )
  );

-- Sync permissions with the schema to generate testimonials:create, testimonials:view, testimonials:edit, testimonials:delete permissions
SELECT sync_permissions_with_schema();

-- Add a function to get featured testimonials (accessible to all users)
CREATE OR REPLACE FUNCTION get_featured_testimonials()
RETURNS JSONB AS $$
DECLARE
  featured_testimonials JSONB;
BEGIN
  -- Use a subquery with ORDER BY to get the testimonials in the correct order
  -- This avoids the GROUP BY issue with the ORDER BY clause
  WITH ordered_testimonials AS (
    SELECT
      t.id,
      t.full_name,
      t.context,
      t.experience,
      t.created_at
    FROM testimonials t
    WHERE t.status = 'approved' AND t.is_featured = true
    ORDER BY t.created_at DESC
  )
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', ot.id,
      'full_name', ot.full_name,
      'context', ot.context,
      'experience', ot.experience,
      'created_at', ot.created_at
    )
  ) INTO featured_testimonials
  FROM ordered_testimonials ot;

  -- Return empty array instead of null if no testimonials found
  RETURN COALESCE(featured_testimonials, '[]'::JSONB);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to all users (including anonymous)
GRANT EXECUTE ON FUNCTION get_featured_testimonials() TO anon;
GRANT EXECUTE ON FUNCTION get_featured_testimonials() TO authenticated;
GRANT EXECUTE ON FUNCTION get_featured_testimonials() TO service_role;

-- Add documentation for the function
COMMENT ON FUNCTION get_featured_testimonials() IS
'Returns a list of featured testimonials that have been approved.
This function is accessible to all users, including anonymous users.
Returns a JSON array of testimonial objects with the following fields:
- id: The testimonial ID
- full_name: The name of the person who gave the testimonial
- context: The context of the testimonial (e.g., "Car Buyer, Kampala")
- experience: The testimonial text
- created_at: The date the testimonial was created
If no testimonials are found, returns an empty array.';
