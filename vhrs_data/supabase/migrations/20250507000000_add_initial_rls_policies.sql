-- Initial RLS Policies
-- Adds security policies and enables <PERSON><PERSON> on all tables

-- Helper function to check if a user has admin role
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid()
    AND r.name = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Admin Policies: Allow admins to perform all actions on all tables

-- Profiles Table
CREATE POLICY admin_all_profiles ON profiles
  FOR ALL USING (public.is_admin());

-- Roles Table
CREATE POLICY admin_all_roles ON roles
  FOR ALL USING (public.is_admin());

-- Permissions Table
CREATE POLICY admin_all_permissions ON permissions
  FOR ALL USING (public.is_admin());

-- Role Permissions Table
CREATE POLICY admin_all_role_permissions ON role_permissions
  FOR ALL USING (public.is_admin());

-- User Roles Table
CREATE POLICY admin_all_user_roles ON user_roles
  FOR ALL USING (public.is_admin());

-- Vehicles Table
CREATE POLICY admin_all_vehicles ON vehicles
  FOR ALL USING (public.is_admin());

-- Vehicle License Plates Table
CREATE POLICY admin_all_vehicle_license_plates ON vehicle_license_plates
  FOR ALL USING (public.is_admin());

-- Vehicle Colors Table
CREATE POLICY admin_all_vehicle_colors ON vehicle_colors
  FOR ALL USING (public.is_admin());

-- Ownership History Table
CREATE POLICY admin_all_ownership_history ON ownership_history
  FOR ALL USING (public.is_admin());

-- Accidents Table
CREATE POLICY admin_all_accidents ON accidents
  FOR ALL USING (public.is_admin());

-- Maintenance Records Table
CREATE POLICY admin_all_maintenance_records ON maintenance_records
  FOR ALL USING (public.is_admin());

-- Mileage Records Table
CREATE POLICY admin_all_mileage_records ON mileage_records
  FOR ALL USING (public.is_admin());

-- Alert Types Table
CREATE POLICY admin_all_alert_types ON alert_types
  FOR ALL USING (public.is_admin());

-- Alerts Table
CREATE POLICY admin_all_alerts ON alerts
  FOR ALL USING (public.is_admin());

-- Alert Media Table
CREATE POLICY admin_all_alert_media ON alert_media
  FOR ALL USING (public.is_admin());

-- History Report Requests Table
CREATE POLICY admin_all_history_report_requests ON history_report_requests
  FOR ALL USING (public.is_admin());

-- Data Submissions Table
CREATE POLICY admin_all_data_submissions ON data_submissions
  FOR ALL USING (public.is_admin());

-- -- Additional Role-Specific Policies

-- -- Data Agents: Edit Vehicles
-- CREATE POLICY vehicle_edit_policy ON vehicles
--   FOR UPDATE USING (
--     EXISTS (SELECT 1 FROM user_roles ur
--             JOIN roles r ON ur.role_id = r.id
--             WHERE ur.user_id = auth.uid()
--               AND r.name = 'data_agent')
--   );

-- RLS is currently disabled for all tables
-- Policies are defined but not enforced until RLS is enabled
-- ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE data_submissions ENABLE ROW LEVEL SECURITY;

-- Add comments explaining the RLS implementation
COMMENT ON FUNCTION public.is_admin() IS 'Helper function to check if the current user has admin role';
COMMENT ON TABLE vehicles IS 'Vehicle records with RLS policies for admin access';
COMMENT ON TABLE data_submissions IS 'Audit logs with RLS policies for admin access';
