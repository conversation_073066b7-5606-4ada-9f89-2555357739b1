-- Initial Database Schema
-- Core tables, indexes, functions and triggers

-- Enable PostGIS for location data
CREATE EXTENSION IF NOT EXISTS postgis;

-- User Management Tables

-- Profiles Table
CREATE TABLE profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT, -- Nullable, but unique when provided
  phone TEXT, -- Nullable, but unique when provided
  full_name TEXT,
  preferences JSONB DEFAULT '{}'::jsonb, -- e.g., {"language":"en", "sms_alerts":true}
  notification_token TEXT, -- For push notifications (FCM/APNS)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  is_verified BOOLEAN DEFAULT false,
  last_active_at TIMESTAMPTZ -- Last user activity
);

-- Optimized Indexes
CREATE UNIQUE INDEX idx_profiles_email_partial ON profiles(email) WHERE email IS NOT NULL;
CREATE UNIQUE INDEX idx_profiles_phone_partial ON profiles(phone) WHERE phone IS NOT NULL;
CREATE INDEX idx_profiles_notification_token ON profiles(notification_token)
  WHERE notification_token IS NOT NULL;

-- Roles Table
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL, -- e.g., 'admin', 'user' 'data_agent'
  description TEXT
);

-- Default Roles
INSERT INTO roles (name, description) VALUES
  ('admin', 'Full system access'),
  ('user', 'Regular user access');

-- Permissions Table
CREATE TABLE permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code TEXT UNIQUE NOT NULL, -- e.g., 'vehicle:create'
  description TEXT NOT NULL
);

-- Role Permissions Table
CREATE TABLE role_permissions (
  role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
  PRIMARY KEY (role_id, permission_id)
);

-- User Roles Table
CREATE TABLE user_roles (
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
  PRIMARY KEY (user_id, role_id)
);

-- Vehicle Data Tables

-- Vehicles Table
CREATE TABLE vehicles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vin TEXT UNIQUE NOT NULL, -- Vehicle Identification Number
  make TEXT NOT NULL, -- e.g., Toyota
  model TEXT NOT NULL, -- e.g., Hilux
  year INTEGER CHECK (year > 1900),
  vehicle_type TEXT CHECK (vehicle_type IN ('car', 'motorcycle', 'truck')),
  added_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance Indexes
CREATE INDEX idx_vehicles_vin ON vehicles(vin);
CREATE INDEX idx_vehicles_make_model ON vehicles(make, model);

-- Vehicle License Plates Table
CREATE TABLE vehicle_license_plates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID NOT NULL REFERENCES vehicles(id),
  license_plate TEXT NOT NULL,
  registration_centre TEXT,  -- e.g., "URA Kampala", "Mbale DTS Office"
  start_date DATE NOT NULL, -- if start is unknown, today is used
  end_date DATE,  -- NULL = currently active
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add constraint to prevent overlapping date ranges for the same vehicle
CREATE EXTENSION IF NOT EXISTS btree_gist;
ALTER TABLE vehicle_license_plates ADD CONSTRAINT no_overlapping_plates
  EXCLUDE USING gist (vehicle_id WITH =, daterange(start_date, end_date, '[]') WITH &&);

-- Indexes
CREATE INDEX idx_plates_vehicle ON vehicle_license_plates(vehicle_id);
CREATE INDEX idx_plates_active ON vehicle_license_plates(license_plate) WHERE end_date IS NULL;

-- Vehicle Colors Table
CREATE TABLE vehicle_colors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID NOT NULL REFERENCES vehicles(id),
  color TEXT NOT NULL,
  effective_date DATE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_colors_vehicle ON vehicle_colors(vehicle_id);

-- Ownership History Table
CREATE TABLE ownership_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  owner_name TEXT NOT NULL,
  owner_phone TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  is_current BOOLEAN DEFAULT true,
  proof_url TEXT -- PDF/image URL of ownership document
);

-- Index for Active Ownership Lookups
CREATE INDEX idx_current_owners ON ownership_history(vehicle_id)
  WHERE is_current = true;

-- Accidents Table
CREATE TABLE accidents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  severity TEXT CHECK (severity IN ('minor', 'major', 'totaled')),
  photos TEXT[], -- Array of image URLs
  reported_by UUID REFERENCES profiles(id)
);

-- Query Optimization
CREATE INDEX idx_accidents_vehicle_date ON accidents(vehicle_id, date DESC);

-- Maintenance Records Table
CREATE TABLE maintenance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  service_type TEXT NOT NULL, -- e.g., 'oil_change'
  date DATE NOT NULL,
  mileage INTEGER,
  garage_name TEXT,
  receipt_url TEXT -- Link to service receipt
);

-- Service History Index
CREATE INDEX idx_maintenance_vehicle ON maintenance_records(vehicle_id, date DESC);

-- Mileage Records Table
CREATE TABLE mileage_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  mileage INTEGER NOT NULL,
  date DATE NOT NULL,
  source TEXT NOT NULL CHECK (source IN ('user', 'garage', 'inspection')),
  is_suspicious BOOLEAN DEFAULT false,
  registered_by UUID REFERENCES profiles(id)
);

-- Fraud Analysis Index
CREATE INDEX idx_mileage_suspicious ON mileage_records(vehicle_id)
  WHERE is_suspicious = true;

-- Alert Types Table
CREATE TABLE alert_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL, -- e.g., 'odometer_rollback'
  description TEXT,          -- Optional description
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Default Alert Types
INSERT INTO alert_types (name, description) VALUES
  ('odometer_rollback', 'Suspected tampering with odometer reading'),
  ('cloned_vin', 'Vehicle with duplicated VIN detected'),
  ('accident_report', 'Unreported accident information'),
  ('stolen_vehicle', 'Vehicle reported as stolen'),
  ('other', 'Other type of alert');

-- Alerts Table
CREATE TABLE alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE, -- nullable
  license_plate TEXT,
  alert_type_id UUID REFERENCES alert_types(id) ON DELETE RESTRICT NOT NULL, -- link to alert_types
  confidence_score FLOAT CHECK (confidence_score >= 0 AND confidence_score <= 1),
  location GEOMETRY(POINT, 4326), -- Latitude/Longitude (WGS84)
  description TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected')),
  resolved_action TEXT, -- e.g., 'moved_to_accidents'
  resolved_by UUID REFERENCES profiles(id),
  created_by UUID REFERENCES profiles(id), -- User who created the alert
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_alerts_location ON alerts USING GIST(location);
CREATE INDEX idx_alerts_type_status ON alerts(alert_type_id, status);
CREATE INDEX idx_alerts_license_plate ON alerts(license_plate);
CREATE INDEX idx_alerts_created_by ON alerts(created_by);

-- Alert Media Table
CREATE TABLE alert_media (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_id UUID REFERENCES alerts(id) ON DELETE CASCADE NOT NULL,
  media_url TEXT NOT NULL,
  media_type TEXT CHECK (media_type IN ('image', 'video', 'document')),
  description TEXT, -- Optional caption/context
  details JSONB DEFAULT '{}'::jsonb, -- e.g., {"source":"iPhone", "f":0.5}
  uploaded_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index for quick media retrieval per alert
CREATE INDEX idx_alert_media_parent ON alert_media(alert_id);

-- History Report Requests Table
CREATE TABLE history_report_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  vehicle_id UUID REFERENCES vehicles(id),
  license_plate TEXT,
  vin TEXT,
  request_method TEXT NOT NULL CHECK (request_method IN ('vin', 'license_plate', 'manual')), -- How user searched
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'generated', 'failed')),
  document_url TEXT, -- URL to downloadable PDF, if generated
  history_report JSONB DEFAULT '{}'::jsonb, -- Stores the generated report as a JSON object
  viewed_in_app BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ, -- When report was generated (for audit/perf)
  processed_by TEXT, -- Identifier of the service that processed this request
  retry_count INTEGER DEFAULT 0, -- Counter for processing attempts (helps with troubleshooting)
  last_error TEXT -- Stores error messages if processing failed
);

-- Indexes
CREATE INDEX idx_report_requests_user ON history_report_requests(user_id, created_at DESC);
CREATE INDEX idx_report_requests_vehicle ON history_report_requests(vehicle_id);
CREATE INDEX idx_history_report_requests_status ON history_report_requests(status) WHERE status = 'pending';

-- System Operations Tables

-- Data Submissions Table (Audit Trail)
CREATE TABLE data_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  table_name TEXT NOT NULL, -- e.g., 'vehicles'
  record_id UUID NOT NULL,
  action TEXT NOT NULL CHECK (action IN ('create', 'update', 'delete')),
  old_data JSONB, -- Previous state (for updates/deletes)
  new_data JSONB, -- New state (for creates/updates)
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Audit Query Optimization
CREATE INDEX idx_data_submissions_user ON data_submissions(user_id, timestamp DESC);

-- Create audit logs trigger function
CREATE OR REPLACE FUNCTION log_data_change()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO data_submissions (user_id, table_name, record_id, action, new_data, timestamp)
    VALUES (auth.uid(), TG_TABLE_NAME, NEW.id, 'create', row_to_json(NEW), NOW());
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO data_submissions (user_id, table_name, record_id, action, old_data, new_data, timestamp)
    VALUES (auth.uid(), TG_TABLE_NAME, NEW.id, 'update', row_to_json(OLD), row_to_json(NEW), NOW());
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO data_submissions (user_id, table_name, record_id, action, old_data, timestamp)
    VALUES (auth.uid(), TG_TABLE_NAME, OLD.id, 'delete', row_to_json(OLD), NOW());
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add triggers for audit logging
CREATE TRIGGER log_vehicles_changes
AFTER INSERT OR UPDATE OR DELETE ON public.vehicles
FOR EACH ROW EXECUTE FUNCTION log_data_change();

CREATE TRIGGER log_vehicle_license_plates_changes
AFTER INSERT OR UPDATE OR DELETE ON public.vehicle_license_plates
FOR EACH ROW EXECUTE FUNCTION log_data_change();

CREATE TRIGGER log_vehicle_colors_changes
AFTER INSERT OR UPDATE OR DELETE ON public.vehicle_colors
FOR EACH ROW EXECUTE FUNCTION log_data_change();

CREATE TRIGGER log_ownership_history_changes
AFTER INSERT OR UPDATE OR DELETE ON public.ownership_history
FOR EACH ROW EXECUTE FUNCTION log_data_change();

CREATE TRIGGER log_accidents_changes
AFTER INSERT OR UPDATE OR DELETE ON public.accidents
FOR EACH ROW EXECUTE FUNCTION log_data_change();

CREATE TRIGGER log_maintenance_records_changes
AFTER INSERT OR UPDATE OR DELETE ON public.maintenance_records
FOR EACH ROW EXECUTE FUNCTION log_data_change();

CREATE TRIGGER log_mileage_records_changes
AFTER INSERT OR UPDATE OR DELETE ON public.mileage_records
FOR EACH ROW EXECUTE FUNCTION log_data_change();

-- Create user role management functions
CREATE OR REPLACE FUNCTION user_has_role(user_id UUID, role_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  has_role BOOLEAN;
BEGIN
  SELECT EXISTS(
    SELECT 1
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_id
    AND r.name = role_name
  ) INTO has_role;

  RETURN has_role;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get all roles for a user
CREATE OR REPLACE FUNCTION get_user_roles(user_id UUID)
RETURNS TEXT[] AS $$
DECLARE
  user_roles TEXT[];
BEGIN
  SELECT array_agg(r.name)
  FROM public.user_roles ur
  JOIN public.roles r ON ur.role_id = r.id
  WHERE ur.user_id = get_user_roles.user_id
  INTO user_roles;

  RETURN user_roles;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users and service role
GRANT EXECUTE ON FUNCTION get_user_roles(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_roles(UUID) TO service_role;

-- Create a function to add a role to a user
CREATE OR REPLACE FUNCTION add_role_to_user(user_id UUID, role_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  role_id UUID;
BEGIN
  -- Get the role ID
  SELECT roles.id INTO role_id FROM public.roles WHERE roles.name = role_name;

  IF role_id IS NULL THEN
    RETURN FALSE; -- Role doesn't exist
  END IF;

  -- Add the role to the user
  INSERT INTO public.user_roles (user_id, role_id)
  VALUES (user_id, role_id)
  ON CONFLICT (user_id, role_id) DO NOTHING;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create a view that joins profiles with their roles for easier querying
CREATE OR REPLACE VIEW user_with_roles AS
SELECT
  u.id,
  u.email,
  u.full_name,
  u.is_verified,
  u.created_at,
  array_agg(r.name) as roles
FROM
  public.profiles u
LEFT JOIN
  public.user_roles ur ON u.id = ur.user_id
LEFT JOIN
  public.roles r ON ur.role_id = r.id
GROUP BY
  u.id, u.email, u.full_name, u.is_verified, u.created_at;

-- Create function to sync user profiles between auth.users and profiles table
CREATE OR REPLACE FUNCTION sync_user_profile()
RETURNS TRIGGER AS $$
DECLARE
  default_role_id UUID;
  user_exists BOOLEAN;
BEGIN
  -- Check if user already exists in the profiles table to avoid duplicates
  SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = NEW.id) INTO user_exists;

  IF user_exists THEN
    -- User already exists, update their information
    UPDATE public.profiles
    SET
      email = NEW.email,
      phone = NEW.phone,
      full_name = COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
      is_verified = NEW.confirmed_at IS NOT NULL
    WHERE id = NEW.id;
  ELSE
    -- User doesn't exist, create a new entry
    INSERT INTO public.profiles (
      id,
      email,
      phone,
      full_name,
      created_at,
      is_verified
    )
    VALUES (
      NEW.id,
      NEW.email,
      NEW.phone,
      COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
      NOW(),
      NEW.confirmed_at IS NOT NULL
    );

    -- Get the default user role ID
    SELECT id INTO default_role_id FROM public.roles WHERE name = 'user';

    IF default_role_id IS NOT NULL THEN
      -- Assign the default role to the user
      INSERT INTO public.user_roles (user_id, role_id)
      VALUES (NEW.id, default_role_id)
      ON CONFLICT (user_id, role_id) DO NOTHING;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user profile sync
CREATE TRIGGER trigger_sync_user_profile
AFTER INSERT OR UPDATE ON auth.users
FOR EACH ROW
EXECUTE FUNCTION sync_user_profile();

-- Add a comment explaining the change
COMMENT ON FUNCTION sync_user_profile() IS 'Syncs user data from auth.users to profiles table on both INSERT and UPDATE operations';
COMMENT ON TABLE public.profiles IS 'User profiles. New users are automatically created by the trigger_sync_user_profile database trigger when they sign up through Supabase Auth.';
