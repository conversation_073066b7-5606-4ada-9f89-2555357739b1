-- Add permission system
-- Adds functions and triggers to automatically generate and manage permissions based on database tables

-- Clear existing permissions to start fresh
TRUNCATE permissions CASCADE;

-- Function to generate permissions based on database tables
CREATE OR REPLACE FUNCTION generate_permissions()
RETURNS TABLE(
  permission_code TEXT,
  permission_description TEXT,
  created BO<PERSON>EAN
) AS $$
DECLARE
  table_record RECORD;
  permission_exists BOOLEAN;
  permission_id UUID;
  action_record RECORD;
  actions TEXT[] := ARRAY['create', 'view', 'edit', 'delete'];
  action_descriptions TEXT[] := ARRAY['Add new', 'View', 'Modify', 'Delete'];
  excluded_tables TEXT[] := ARRAY[
    -- System tables
    'schema_migrations',
    'spatial_ref_sys',
    'pg_stat_statements',

    -- Tables managed through special permissions
    'permissions',
    'role_permissions',
    'user_roles',
    'profiles',
    'data_submissions'
  ];
BEGIN
  -- Loop through all tables in the public schema
  FOR table_record IN
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
    AND table_name NOT IN (SELECT unnest(excluded_tables))
  LOOP
    -- For each table, create standard CRUD permissions
    FOR i IN 1..array_length(actions, 1) LOOP
      -- Format: table:action (e.g., vehicle:create)
      permission_code := table_record.table_name || ':' || actions[i];
      permission_description := action_descriptions[i] || ' ' || replace(table_record.table_name, '_', ' ') || ' (' || table_record.table_name || ')';

      -- Check if permission already exists
      SELECT EXISTS(
        SELECT 1 FROM permissions WHERE code = permission_code
      ) INTO permission_exists;

      IF NOT permission_exists THEN
        -- Insert new permission
        INSERT INTO permissions (code, description)
        VALUES (permission_code, permission_description)
        RETURNING id INTO permission_id;

        -- Return the created permission
        RETURN QUERY SELECT permission_code, permission_description, TRUE;
      ELSE
        -- Return existing permission
        RETURN QUERY SELECT permission_code, permission_description, FALSE;
      END IF;
    END LOOP;
  END LOOP;

  -- Add special system-wide permissions if they don't exist
  FOR action_record IN
    SELECT * FROM (VALUES
      ('user:manage', 'Manage users and roles (system)'),
      ('audit:view', 'View audit logs (system)')
    ) AS special_permissions(code, description)
  LOOP
    -- Check if special permission already exists
    SELECT EXISTS(
      SELECT 1 FROM permissions WHERE code = action_record.code
    ) INTO permission_exists;

    IF NOT permission_exists THEN
      -- Insert new special permission
      INSERT INTO permissions (code, description)
      VALUES (action_record.code, action_record.description)
      RETURNING id INTO permission_id;

      -- Return the created permission
      RETURN QUERY SELECT action_record.code, action_record.description, TRUE;
    ELSE
      -- Return existing permission
      RETURN QUERY SELECT action_record.code, action_record.description, FALSE;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to assign default permissions to roles
CREATE OR REPLACE FUNCTION assign_default_role_permissions()
RETURNS VOID AS $$
DECLARE
  admin_role_id UUID;
  user_role_id UUID;
  permission_record RECORD;
BEGIN
  -- Get role IDs
  SELECT id INTO admin_role_id FROM roles WHERE name = 'admin';
  SELECT id INTO user_role_id FROM roles WHERE name = 'user';

  -- Assign all permissions to admin role
  FOR permission_record IN SELECT id FROM permissions LOOP
    INSERT INTO role_permissions (role_id, permission_id)
    VALUES (admin_role_id, permission_record.id)
    ON CONFLICT (role_id, permission_id) DO NOTHING;
  END LOOP;

  -- Assign basic view permissions to user role
  FOR permission_record IN
    SELECT id FROM permissions
    WHERE code LIKE '%:view'
    AND (
      code LIKE 'vehicle%' OR
      code LIKE 'alert%' OR
      code LIKE 'history%' OR
      code LIKE 'mileage%'
    )
  LOOP
    INSERT INTO role_permissions (role_id, permission_id)
    VALUES (user_role_id, permission_record.id)
    ON CONFLICT (role_id, permission_id) DO NOTHING;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle table creation events
CREATE OR REPLACE FUNCTION handle_table_creation()
RETURNS event_trigger AS $$
DECLARE
  obj RECORD;
  table_name TEXT;
  permission_code TEXT;
  permission_description TEXT;
  permission_exists BOOLEAN;
  permission_id UUID;
  actions TEXT[] := ARRAY['create', 'view', 'edit', 'delete'];
  action_descriptions TEXT[] := ARRAY['Add new', 'View', 'Modify', 'Delete'];
  excluded_tables TEXT[] := ARRAY[
    -- System tables
    'schema_migrations',
    'spatial_ref_sys',
    'pg_stat_statements',

    -- Tables managed through special permissions
    'permissions',
    'role_permissions',
    'user_roles',
    'profiles',
    'data_submissions'
  ];
BEGIN
  -- Loop through created tables
  FOR obj IN SELECT * FROM pg_event_trigger_ddl_commands() WHERE command_tag = 'CREATE TABLE' LOOP
    -- Extract table name from the object identity
    table_name := regexp_replace(obj.object_identity, '.*\.', '');

    -- Skip excluded tables
    IF table_name = ANY(excluded_tables) THEN
      CONTINUE;
    END IF;

    -- For each table, create standard CRUD permissions
    FOR i IN 1..array_length(actions, 1) LOOP
      -- Format: table:action (e.g., vehicle:create)
      permission_code := table_name || ':' || actions[i];
      permission_description := action_descriptions[i] || ' ' || replace(table_name, '_', ' ') || ' (' || table_name || ')';

      -- Check if permission already exists
      SELECT EXISTS(
        SELECT 1 FROM permissions WHERE code = permission_code
      ) INTO permission_exists;

      IF NOT permission_exists THEN
        -- Insert new permission
        INSERT INTO permissions (code, description)
        VALUES (permission_code, permission_description)
        RETURNING id INTO permission_id;

        -- Log the creation
        RAISE NOTICE 'Created permission: %', permission_code;
      END IF;
    END LOOP;

    -- Assign default permissions to roles
    PERFORM assign_default_role_permissions();
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to handle table drop events
CREATE OR REPLACE FUNCTION handle_table_drop()
RETURNS event_trigger AS $$
DECLARE
  obj RECORD;
  table_name TEXT;
  permission_prefix TEXT;
BEGIN
  -- Loop through dropped tables
  FOR obj IN SELECT * FROM pg_event_trigger_dropped_objects() WHERE object_type = 'table' LOOP
    -- Extract table name
    table_name := obj.object_name;

    -- Create permission prefix for this table
    permission_prefix := table_name || ':';

    -- Delete all permissions related to this table
    DELETE FROM permissions
    WHERE code LIKE (permission_prefix || '%');

    -- Log the deletion
    RAISE NOTICE 'Deleted permissions for table: %', table_name;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to sync permissions with existing tables
CREATE OR REPLACE FUNCTION sync_permissions_with_schema()
RETURNS VOID AS $$
BEGIN
  -- Generate permissions for all existing tables
  PERFORM generate_permissions();

  -- Assign default permissions to roles
  PERFORM assign_default_role_permissions();

  RAISE NOTICE 'Permissions synchronized with database schema';
END;
$$ LANGUAGE plpgsql;

-- RPC function to generate permissions from the frontend
CREATE OR REPLACE FUNCTION generate_system_permissions()
RETURNS JSONB AS $$
DECLARE
  result_record RECORD;
  result_array JSONB := '[]'::JSONB;
  is_admin BOOLEAN;
BEGIN
  -- Check if the current user is an admin
  SELECT public.is_admin() INTO is_admin;

  IF NOT is_admin THEN
    RAISE EXCEPTION 'Only administrators can generate system permissions';
  END IF;

  -- Generate permissions
  FOR result_record IN SELECT * FROM generate_permissions() LOOP
    result_array := result_array || jsonb_build_object(
      'code', result_record.permission_code,
      'description', result_record.permission_description,
      'created', result_record.created
    );
  END LOOP;

  -- Assign default permissions to roles
  PERFORM assign_default_role_permissions();

  -- Return the results
  RETURN jsonb_build_object(
    'success', TRUE,
    'message', 'Permissions generated successfully',
    'permissions', result_array
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', FALSE,
      'message', SQLERRM,
      'permissions', '[]'::JSONB
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the get_user_permissions function to use the database permissions
-- This replaces the previous hardcoded implementation
-- IMPORTANT: This function uses auth.uid() to get the current user's ID.
-- When called from an Edge Function or any context using the service role,
-- it will check permissions for the service role, NOT the end user.
-- For Edge Functions, use check_permission() with an explicit user_id instead.
CREATE OR REPLACE FUNCTION get_user_permissions()
RETURNS JSONB AS $$
DECLARE
  current_user_id UUID;
  permissions_json JSONB := '{}'::JSONB;
  permission_record RECORD;
BEGIN
  -- Get the current user's ID (the user making the request)
  -- NOTE: When called with service_role, this will be the service role, not the end user
  current_user_id := auth.uid();

  -- Get all permissions for the user based on their roles
  -- Use table aliases to avoid ambiguous column references
  FOR permission_record IN
    SELECT DISTINCT p.code
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = current_user_id
  LOOP
    permissions_json := permissions_json || jsonb_build_object(permission_record.code, TRUE);
  END LOOP;

  -- Return the permissions as a JSONB object
  RETURN jsonb_build_object('permissions', permissions_json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- NOTE: Event triggers are commented out as they require superuser privileges
-- which are not available in the Supabase local development environment.
-- Instead, we'll rely on the sync_permissions_with_schema() function to be called manually
-- or through the generate_system_permissions() RPC function that is being called on the web dashboard
-- via a Sync permissions button calling the generate_system_permissions() function

-- In production later, we can uncomment the event triggers and test if they work.
-- If not, we can keep the manual approach.

-- -- Create event trigger for table creation
-- DROP EVENT TRIGGER IF EXISTS trigger_table_creation CASCADE;
-- CREATE EVENT TRIGGER trigger_table_creation
-- ON ddl_command_end
-- WHEN tag IN ('CREATE TABLE')
-- EXECUTE FUNCTION handle_table_creation();
--
-- -- Create event trigger for table drop
-- DROP EVENT TRIGGER IF EXISTS trigger_table_drop CASCADE;
-- CREATE EVENT TRIGGER trigger_table_drop
-- ON sql_drop
-- WHEN tag IN ('DROP TABLE')
-- EXECUTE FUNCTION handle_table_drop();

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION generate_permissions() TO service_role;
GRANT EXECUTE ON FUNCTION assign_default_role_permissions() TO service_role;
-- Event trigger functions are kept but not used as triggers
GRANT EXECUTE ON FUNCTION handle_table_creation() TO service_role;
GRANT EXECUTE ON FUNCTION handle_table_drop() TO service_role;
GRANT EXECUTE ON FUNCTION sync_permissions_with_schema() TO service_role;
GRANT EXECUTE ON FUNCTION generate_system_permissions() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_permissions() TO authenticated;

-- Add documentation for functions
COMMENT ON FUNCTION get_user_permissions() IS
'Returns all permissions for the authenticated user based on their roles.
Permissions follow the format table:action (e.g., vehicle:create, alert:view).
The function returns a JSON object with permission keys and boolean values.
CAUTION: When called from Edge Functions or with service_role, this checks
permissions for the service role, not the end user. For Edge Functions,
use check_permission() with an explicit user_id instead.';

COMMENT ON FUNCTION generate_system_permissions() IS
'Generates permissions based on database tables and assigns them to roles.
Only administrators can call this function.
Returns a JSON object with the results of the operation.';

COMMENT ON FUNCTION sync_permissions_with_schema() IS
'Synchronizes permissions with the current database schema.
This function is called automatically when tables are created or dropped.
It can also be called manually to refresh permissions.';

COMMENT ON FUNCTION generate_permissions() IS
'Scans the database schema and generates standard CRUD permissions for each table.
Returns a table with the generated permissions and whether they were newly created.';

COMMENT ON FUNCTION assign_default_role_permissions() IS
'Assigns default permissions to the core roles (admin, user).
Admin gets all permissions, and user gets basic view permissions for vehicle, alert, history, and mileage records.
Additional roles and their permissions can be managed by admins through the UI.';

-- Create a function to check if a user has a specific permission
-- IMPORTANT: This function accepts an explicit user_id parameter, making it
-- suitable for use in Edge Functions and service role contexts where you need
-- to check permissions for a specific user rather than the calling context.
-- For Edge Functions using service_role, ALWAYS pass the user_id parameter
-- to ensure you're checking the correct user's permissions.
CREATE OR REPLACE FUNCTION check_permission(permission_code TEXT, user_id UUID DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
  current_user_id UUID;
  has_permission BOOLEAN;
BEGIN
  -- Use provided user_id or get the current user's ID if not provided
  -- When called from Edge Functions with service_role, ALWAYS provide user_id
  -- to check the end user's permissions, not the service role's
  current_user_id := COALESCE(user_id, auth.uid());

  -- Check if the user has the specified permission through any of their roles
  SELECT EXISTS(
    SELECT 1
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = current_user_id
    AND p.code = permission_code
  ) INTO has_permission;

  RETURN has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users and service role
GRANT EXECUTE ON FUNCTION check_permission(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_permission(TEXT, UUID) TO service_role;

-- Add documentation for check_permission function
COMMENT ON FUNCTION check_permission(TEXT, UUID) IS
'Checks if a user has a specific permission based on their roles.
Parameters:
  - permission_code: The permission code to check (e.g., "testimonials:edit")
  - user_id: Optional user ID to check permissions for. If not provided, uses the current authenticated user.
    IMPORTANT: When called from Edge Functions or with service_role, ALWAYS provide this parameter
    to check the end user''s permissions rather than the service role''s permissions.
Returns TRUE if the user has the permission, FALSE otherwise.
This is the preferred function for permission checks in Edge Functions and service role contexts.';

-- Sync permissions with the current database schema
SELECT sync_permissions_with_schema();
