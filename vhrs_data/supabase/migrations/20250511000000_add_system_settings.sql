-- Migration: Add System Settings Table
-- This migration adds a system_settings table for storing application-wide settings with versioning

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
  id SERIAL PRIMARY KEY,
  system_name VARCHAR(255) NOT NULL DEFAULT 'Vehicle History Record System',
  contact_email VARCHAR(255),
  support_phone VARCHAR(50),
  maintenance_mode BOOLEAN DEFAULT FALSE,
  maintenance_message TEXT,

  -- <PERSON><PERSON> settings
  default_alert_status VARCHAR(50) DEFAULT 'pending',
  require_approval BOOLEAN DEFAULT TRUE,
  notify_admins BOOLEAN DEFAULT TRUE,
  alert_categories TEXT DEFAULT 'accident,fraud,theft,damage,other',

  -- Security settings
  max_login_attempts INTEGER DEFAULT 5,
  session_timeout INTEGER DEFAULT 60,
  require_email_verification BOOLEAN DEFAULT TRUE,
  allow_public_registration BOOLEAN DEFAULT TRUE,

  -- Metadata
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

-- Function to get the system admin user ID
CREATE OR REPLACE FUNCTION get_system_admin_id()
RETURNS UUID AS $$
DECLARE
  admin_id UUID;
BEGIN
  SELECT id INTO admin_id
  FROM auth.users
  WHERE email = '<EMAIL>'
  LIMIT 1;

  RETURN admin_id;
END;
$$ LANGUAGE plpgsql;

-- Insert default settings with system admin as creator
INSERT INTO system_settings (
  system_name,
  contact_email,
  created_by
)
VALUES (
  'Vehicle History Record System',
  '<EMAIL>',
  get_system_admin_id()
);

-- Add RLS policies
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Only admins can perform all operations on system settings
CREATE POLICY admin_manage_system_settings ON system_settings
  FOR ALL USING (public.is_admin());

-- Add a trigger to mark old settings as inactive when new ones are added
CREATE OR REPLACE FUNCTION mark_old_settings_inactive()
RETURNS TRIGGER AS $$
BEGIN
  -- Mark all other settings as inactive
  UPDATE system_settings
  SET is_active = FALSE
  WHERE id != NEW.id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER mark_old_settings_inactive_trigger
AFTER INSERT ON system_settings
FOR EACH ROW
EXECUTE FUNCTION mark_old_settings_inactive();

-- Add a function to get public system settings (accessible to all users, including non-authenticated)
CREATE OR REPLACE FUNCTION get_public_system_settings()
RETURNS JSONB AS $$
DECLARE
  public_settings JSONB;
BEGIN
  SELECT jsonb_build_object(
    'system_name', s.system_name,
    'contact_email', s.contact_email,
    'support_phone', s.support_phone,
    'maintenance_mode', s.maintenance_mode,
    'maintenance_message', s.maintenance_message,
    'allow_public_registration', s.allow_public_registration,
    'alert_categories', s.alert_categories
  ) INTO public_settings
  FROM system_settings s
  WHERE is_active = TRUE
  ORDER BY id DESC
  LIMIT 1;

  RETURN public_settings;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to all users (including anonymous)
GRANT EXECUTE ON FUNCTION get_public_system_settings() TO anon;
GRANT EXECUTE ON FUNCTION get_public_system_settings() TO authenticated;
GRANT EXECUTE ON FUNCTION get_public_system_settings() TO service_role;
