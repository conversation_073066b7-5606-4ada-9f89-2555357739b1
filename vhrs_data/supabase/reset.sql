-- Reset script for the Vehicle History Record System database
-- This script drops all tables, functions, triggers, and extensions

-- First, drop all triggers
DROP TRIGGER IF EXISTS trigger_sync_user_profile ON auth.users;
DROP TRIGGER IF EXISTS log_vehicles_changes ON vehicles;
DROP TRIGGER IF EXISTS log_vehicle_license_plates_changes ON vehicle_license_plates;
DROP TRIGGER IF EXISTS log_vehicle_colors_changes ON vehicle_colors;
DROP TRIGGER IF EXISTS log_ownership_history_changes ON ownership_history;
DROP TRIGGER IF EXISTS log_accidents_changes ON accidents;
DROP TRIGGER IF EXISTS log_maintenance_records_changes ON maintenance_records;
DROP TRIGGER IF EXISTS log_mileage_records_changes ON mileage_records;

-- Drop all views
DROP VIEW IF EXISTS user_with_roles CASCADE;

-- Drop all functions
DROP FUNCTION IF EXISTS log_data_change() CASCADE;
DROP FUNCTION IF EXISTS user_has_role(UUID, TEXT) CASCADE;
DROP FUNCTION IF EXISTS get_user_roles(UUID) CASCADE;
DROP FUNCTION IF EXISTS sync_user_profile() CASCADE;
DROP FUNCTION IF EXISTS auth.is_admin() CASCADE;

-- Drop all tables in reverse order of dependencies
DROP TABLE IF EXISTS data_submissions CASCADE;
DROP TABLE IF EXISTS history_report_requests CASCADE;
DROP TABLE IF EXISTS alert_media CASCADE;
DROP TABLE IF EXISTS alerts CASCADE;
DROP TABLE IF EXISTS alert_types CASCADE;
DROP TABLE IF EXISTS mileage_records CASCADE;
DROP TABLE IF EXISTS maintenance_records CASCADE;
DROP TABLE IF EXISTS accidents CASCADE;
DROP TABLE IF EXISTS ownership_history CASCADE;
DROP TABLE IF EXISTS vehicle_colors CASCADE;
DROP TABLE IF EXISTS vehicle_license_plates CASCADE;
DROP TABLE IF EXISTS vehicles CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS role_permissions CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- Drop all policies
DROP POLICY IF EXISTS vehicle_delete_policy ON vehicles;
DROP POLICY IF EXISTS vehicle_edit_policy ON vehicles;
DROP POLICY IF EXISTS audit_logs_admin_policy ON data_submissions;
DROP POLICY IF EXISTS users_self_access ON profiles;
DROP POLICY IF EXISTS users_admin_access ON profiles;

-- Drop admin policies
DROP POLICY IF EXISTS admin_all_profiles ON profiles;
DROP POLICY IF EXISTS admin_all_roles ON roles;
DROP POLICY IF EXISTS admin_all_permissions ON permissions;
DROP POLICY IF EXISTS admin_all_role_permissions ON role_permissions;
DROP POLICY IF EXISTS admin_all_user_roles ON user_roles;
DROP POLICY IF EXISTS admin_all_vehicles ON vehicles;
DROP POLICY IF EXISTS admin_all_vehicle_license_plates ON vehicle_license_plates;
DROP POLICY IF EXISTS admin_all_vehicle_colors ON vehicle_colors;
DROP POLICY IF EXISTS admin_all_ownership_history ON ownership_history;
DROP POLICY IF EXISTS admin_all_accidents ON accidents;
DROP POLICY IF EXISTS admin_all_maintenance_records ON maintenance_records;
DROP POLICY IF EXISTS admin_all_mileage_records ON mileage_records;
DROP POLICY IF EXISTS admin_all_alert_types ON alert_types;
DROP POLICY IF EXISTS admin_all_alerts ON alerts;
DROP POLICY IF EXISTS admin_all_alert_media ON alert_media;
DROP POLICY IF EXISTS admin_all_history_report_requests ON history_report_requests;
DROP POLICY IF EXISTS admin_all_data_submissions ON data_submissions;

-- Drop extensions
DROP EXTENSION IF EXISTS postgis CASCADE;
DROP EXTENSION IF EXISTS btree_gist CASCADE;
DROP EXTENSION IF EXISTS http CASCADE;

-- To reset the database completely, run:
-- supabase db reset
