// Supabase Edge Function for managing testimonials
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';

// Define request types
interface TestimonialRequest {
  action: 'get_featured' | 'get_all' | 'approve' | 'reject' | 'feature' | 'submit' | 'admin_create';
  testimonial_id?: string;
  testimonial_data?: {
    full_name: string;
    context?: string;
    experience: string;
    is_featured?: boolean;
  };
}

// Create Supabase client
const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

// Main function handler
Deno.serve(async (req: Request) => {
  // Define CORS headers to be used in all responses
  const corsHeaders = {
    'Access-Control-Allow-Origin': 'http://localhost:3000',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  };

  try {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
      return new Response('ok', { headers: corsHeaders });
    }

    // Parse request first to check if it's a public action
    const requestData = await req.json() as TestimonialRequest;
    const { action, testimonial_id, testimonial_data } = requestData;

    // For get_featured action, we don't need authentication
    if (action === 'get_featured') {
      // Get featured testimonials for public display
      const { data: featuredData, error: featuredError } = await supabaseClient
        .from('testimonials')
        .select('id, full_name, context, experience, created_at')
        .eq('status', 'approved')
        .eq('is_featured', true)
        .order('created_at', { ascending: false });

      if (featuredError) throw featuredError;
      return new Response(JSON.stringify({ testimonials: featuredData }), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }

    // For all other actions, require authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }

    // Create authenticated client using user's JWT
    const token = authHeader.replace('Bearer ', '');
    const { data: { user } } = await supabaseClient.auth.getUser(token);

    if (!user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }

    // Handle different actions
    switch (action) {

      case 'get_all':
        // Check if user has permission to manage testimonials
        const { data: hasPermission, error: permissionError } = await supabaseClient
          .rpc('check_permission', { permission_code: 'testimonials:edit', user_id: user.id });

        if (permissionError) {
          console.error('Error checking permissions:', permissionError);
          return new Response(JSON.stringify({ error: 'Error checking permissions' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        if (!hasPermission) {
          return new Response(JSON.stringify({ error: 'You do not have permission to manage testimonials' }), {
            status: 403,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // Get all testimonials for admin management
        const { data: allData, error: allError } = await supabaseClient
          .from('testimonials')
          .select('*, profiles!testimonials_user_id_fkey(full_name, email)')
          .order('created_at', { ascending: false });

        if (allError) throw allError;
        return new Response(JSON.stringify({ testimonials: allData }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      case 'approve':
        // Check if user has permission to manage testimonials using check_permission
        const { data: hasApprovePermission, error: approvePermError } = await supabaseClient
          .rpc('check_permission', {
            permission_code: 'testimonials:edit',
            user_id: user.id
          });

        if (approvePermError) {
          console.error('Error checking permissions:', approvePermError);
          return new Response(JSON.stringify({ error: 'Error checking permissions' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        if (!hasApprovePermission) {
          return new Response(JSON.stringify({ error: 'You do not have permission to approve testimonials' }), {
            status: 403,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // Approve a testimonial
        if (!testimonial_id) {
          return new Response(JSON.stringify({ error: 'Missing testimonial_id' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        const { error: approveError } = await supabaseClient
          .from('testimonials')
          .update({
            status: 'approved',
            approved_at: new Date().toISOString(),
            approved_by: user.id
          })
          .eq('id', testimonial_id);

        if (approveError) throw approveError;
        return new Response(JSON.stringify({ success: true }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      case 'reject':
        // Check if user has permission to manage testimonials using check_permission
        const { data: hasRejectPermission, error: rejectPermError } = await supabaseClient
          .rpc('check_permission', {
            permission_code: 'testimonials:edit',
            user_id: user.id
          });

        if (rejectPermError) {
          console.error('Error checking permissions:', rejectPermError);
          return new Response(JSON.stringify({ error: 'Error checking permissions' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        if (!hasRejectPermission) {
          return new Response(JSON.stringify({ error: 'You do not have permission to reject testimonials' }), {
            status: 403,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // Reject a testimonial
        if (!testimonial_id) {
          return new Response(JSON.stringify({ error: 'Missing testimonial_id' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        const { error: rejectError } = await supabaseClient
          .from('testimonials')
          .update({
            status: 'rejected',
            approved_at: new Date().toISOString(),
            approved_by: user.id
          })
          .eq('id', testimonial_id);

        if (rejectError) throw rejectError;
        return new Response(JSON.stringify({ success: true }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      case 'feature':
        // Check if user has permission to manage testimonials using check_permission
        const { data: hasFeaturePermission, error: featurePermError } = await supabaseClient
          .rpc('check_permission', {
            permission_code: 'testimonials:edit',
            user_id: user.id
          });

        if (featurePermError) {
          console.error('Error checking permissions:', featurePermError);
          return new Response(JSON.stringify({ error: 'Error checking permissions' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        if (!hasFeaturePermission) {
          return new Response(JSON.stringify({ error: 'You do not have permission to feature testimonials' }), {
            status: 403,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // Toggle featured status
        if (!testimonial_id) {
          return new Response(JSON.stringify({ error: 'Missing testimonial_id' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // Get current featured status
        const { data: currentData, error: currentError } = await supabaseClient
          .from('testimonials')
          .select('is_featured')
          .eq('id', testimonial_id)
          .single();

        if (currentError) throw currentError;

        // Toggle the status
        const { error: featureError } = await supabaseClient
          .from('testimonials')
          .update({
            is_featured: !currentData.is_featured
          })
          .eq('id', testimonial_id);

        if (featureError) throw featureError;
        return new Response(JSON.stringify({ success: true, is_featured: !currentData.is_featured }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      case 'submit':
        // Submit a new testimonial
        if (!testimonial_data) {
          return new Response(JSON.stringify({ error: 'Missing testimonial data' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        const { error: submitError } = await supabaseClient
          .from('testimonials')
          .insert({
            user_id: user.id,
            full_name: testimonial_data.full_name,
            context: testimonial_data.context,
            experience: testimonial_data.experience,
            status: 'pending'
          });

        if (submitError) throw submitError;
        return new Response(JSON.stringify({ success: true }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      case 'admin_create':
        // Check if user has permission to manage testimonials
        const { data: hasAdminCreatePermission, error: adminCreatePermError } = await supabaseClient
          .rpc('check_permission', {
            permission_code: 'testimonials:edit',
            user_id: user.id
          });

        if (adminCreatePermError) {
          console.error('Error checking permissions:', adminCreatePermError);
          return new Response(JSON.stringify({ error: 'Error checking permissions' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        if (!hasAdminCreatePermission) {
          return new Response(JSON.stringify({ error: 'You do not have permission to create testimonials' }), {
            status: 403,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // Create a new testimonial as admin
        if (!testimonial_data) {
          return new Response(JSON.stringify({ error: 'Missing testimonial data' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        const { error: adminCreateError } = await supabaseClient
          .from('testimonials')
          .insert({
            full_name: testimonial_data.full_name,
            context: testimonial_data.context,
            experience: testimonial_data.experience,
            is_featured: testimonial_data.is_featured || false,
            status: 'approved', // Auto-approve admin-created testimonials
            approved_at: new Date().toISOString(),
            approved_by: user.id
          });

        if (adminCreateError) throw adminCreateError;
        return new Response(JSON.stringify({ success: true }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
});
