-- Seed data for testing the Vehicle History Record System

-- Insert test users
INSERT INTO profiles (id, email, phone, full_name, is_verified)
VALUES
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>', '+256700000001', 'System Administrator', true),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>', '+256700000002', 'Data Agent', true),
  ('33333333-3333-3333-3333-333333333333', '<EMAIL>', '+256700000003', 'Regular User 1', true),
  ('44444444-4444-4444-4444-444444444444', '<EMAIL>', '+256700000004', 'Regular User 2', true),
  ('55555555-5555-5555-5555-555555555555', '<EMAIL>', '+256700000005', 'Regular User 3', true);

-- Create data-agent role for testing
INSERT INTO roles (name, description)
VALUES ('data_agent', 'Data agents can view all data and edit specific tables')
ON CONFLICT (name) DO NOTHING;

-- Assign roles to users
INSERT INTO user_roles (user_id, role_id)
VALUES
  ('11111111-1111-1111-1111-111111111111', (SELECT id FROM roles WHERE name = 'admin')),
  ('22222222-2222-2222-2222-222222222222', (SELECT id FROM roles WHERE name = 'data_agent')),
  ('33333333-3333-3333-3333-333333333333', (SELECT id FROM roles WHERE name = 'user')),
  ('44444444-4444-4444-4444-444444444444', (SELECT id FROM roles WHERE name = 'user')),
  ('55555555-5555-5555-5555-555555555555', (SELECT id FROM roles WHERE name = 'user'));

-- Assign view permissions to data-agent role
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM roles WHERE name = 'data_agent'),
  p.id
FROM permissions p
WHERE p.code LIKE '%:view'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign create/edit permissions for specific tables to data-agent role
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM roles WHERE name = 'data_agent'),
  p.id
FROM permissions p
WHERE p.code IN (
  -- Vehicle-related permissions
  'vehicle:create', 'vehicle:edit',
  'vehicle_colors:create', 'vehicle_colors:edit',
  'vehicle_license_plates:create', 'vehicle_license_plates:edit',

  -- Alert-related permissions
  'alert:create', 'alert:edit',
  'alert_media:create', 'alert_media:edit',

  -- History-related permissions
  'history_report_requests:create', 'history_report_requests:edit',
  'mileage_records:create', 'mileage_records:edit'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Insert test vehicles
INSERT INTO vehicles (id, vin, make, model, year, vehicle_type, added_by)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'JT2BF22K1W0123456', 'Toyota', 'Corolla', 2018, 'car', '22222222-2222-2222-2222-222222222222'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'WBAAM3339XFP12345', 'BMW', '320i', 2020, 'car', '22222222-2222-2222-2222-222222222222'),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'KMHDN45D12U123456', 'Hyundai', 'Elantra', 2019, 'car', '22222222-2222-2222-2222-222222222222'),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '1FMZU73K74ZA12345', 'Ford', 'Ranger', 2017, 'truck', '22222222-2222-2222-2222-222222222222'),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'JH2PC35051M123456', 'Honda', 'CBR600', 2021, 'motorcycle', '22222222-2222-2222-2222-222222222222');

-- Insert license plates
INSERT INTO vehicle_license_plates (vehicle_id, license_plate, registration_centre, start_date, end_date)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'UAA 123A', 'URA Kampala', '2018-05-15', '2020-05-14'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'UAZ 456B', 'URA Kampala', '2020-05-15', NULL),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'UBR 789C', 'URA Kampala', '2024-01-10', NULL),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'UA 012AA', 'URA Entebbe', '2025-01-22', NULL),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'UA 345AE', 'URA Jinja', '2025-01-05', NULL),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'UBE 678F', 'URA Mbale', '2021-03-18', NULL);

-- Insert vehicle colors
INSERT INTO vehicle_colors (vehicle_id, color, effective_date)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Silver', '2018-05-15'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Black', '2024-01-10'),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'White', '2025-01-22'),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'Blue', '2025-01-05'),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'Red', '2021-03-18');

-- Insert ownership history
INSERT INTO ownership_history (vehicle_id, owner_name, owner_phone, start_date, end_date, is_current)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'John Doe', '+256701234567', '2018-05-15', '2021-06-30', false),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Jane Smith', '+256707654321', '2021-07-01', NULL, true),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Robert Johnson', '+256708765432', '2024-01-10', NULL, true),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Mary Williams', '+256709876543', '2025-01-22', NULL, true),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'David Brown', '+256701122334', '2025-01-05', NULL, true),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'Sarah Davis', '+256705566778', '2021-03-18', NULL, true);

-- Insert accident records
INSERT INTO accidents (vehicle_id, date, severity, reported_by)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '2019-08-12', 'minor', '33333333-3333-3333-3333-333333333333'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '2024-03-15', 'major', '33333333-3333-3333-3333-333333333333'),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '2025-02-17', 'minor', '33333333-3333-3333-3333-333333333333');

-- Insert maintenance records
INSERT INTO maintenance_records (vehicle_id, service_type, date, mileage, garage_name)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'oil_change', '2019-02-15', 10000, 'Toyota Service Center'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'brake_service', '2019-08-20', 20000, 'Toyota Service Center'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'oil_change', '2020-02-18', 30000, 'Quick Lube'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'oil_change', '2024-03-10', 10000, 'BMW Service Center'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'tire_replacement', '2024-06-05', 20000, 'Tire World'),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'oil_change', '2025-03-22', 10000, 'Hyundai Service Center'),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'transmission_service', '2025-03-12', 25000, 'Ford Service Center'),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'oil_change', '2021-09-10', 5000, 'Honda Service Center');

-- Insert mileage records
INSERT INTO mileage_records (vehicle_id, mileage, date, source)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 0, '2018-05-15', 'user'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 10000, '2019-02-15', 'garage'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 20000, '2019-08-20', 'garage'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 30000, '2020-02-18', 'garage'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 45000, '2021-06-30', 'inspection'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 0, '2024-01-10', 'user'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 10000, '2024-03-10', 'garage'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 20000, '2024-06-05', 'garage'),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 0, '2025-01-22', 'user'),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 10000, '2025-03-22', 'garage'),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 0, '2025-01-05', 'user'),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 25000, '2025-03-12', 'garage'),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 0, '2021-03-18', 'user'),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 5000, '2021-09-10', 'garage');

-- Insert a suspicious mileage record (potential fraud)
INSERT INTO mileage_records (vehicle_id, mileage, date, source, is_suspicious, registered_by)
VALUES
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 15000, '2024-05-15', 'user', true, '33333333-3333-3333-3333-333333333333');

-- Insert alerts
INSERT INTO alerts (vehicle_id, alert_type_id, confidence_score, description, status, resolved_by)
VALUES
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', (SELECT id FROM alert_types WHERE name = 'odometer_rollback'), 0.85, 'Possible odometer rollback detected. Mileage decreased from 20,000 to 15,000 km.', 'confirmed', '33333333-3333-3333-3333-333333333333'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', (SELECT id FROM alert_types WHERE name = 'stolen_vehicle'), 0.95, 'Vehicle reported stolen in Kampala on 2022-01-15.', 'pending', NULL);

-- Insert alert media
INSERT INTO alert_media (alert_id, media_url, media_type, description, details)
VALUES
  ((SELECT id FROM alerts WHERE vehicle_id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb' AND alert_type_id = (SELECT id FROM alert_types WHERE name = 'odometer_rollback')),
   'https://storage.example.com/alerts/odometer1.jpg',
   'image',
   'Photo of tampered odometer',
   '{"source":"iPhone 13", "f":2.8, "exposure":"1/60", "iso":400}'::jsonb),
  ((SELECT id FROM alerts WHERE vehicle_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa' AND alert_type_id = (SELECT id FROM alert_types WHERE name = 'stolen_vehicle')),
   'https://storage.example.com/alerts/stolen1.jpg',
   'image',
   'Last known location',
   '{"source":"Samsung Galaxy S22", "location":{"lat":0.347596, "lng":32.582520}}'::jsonb);

-- Insert history report requests
INSERT INTO history_report_requests (user_id, vehicle_id, request_method, status, viewed_in_app, history_report)
VALUES
  ('55555555-5555-5555-5555-555555555555', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'vin', 'generated', true,
   '{"vin":"JT2BF22K1W0123456", "make":"Toyota", "model":"Corolla", "year":2018, "owner":"Jane Smith", "accidents":1, "maintenance_records":3}'::jsonb),
  ('55555555-5555-5555-5555-555555555555', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'license_plate', 'pending', false, '{}'::jsonb);

-- Insert audit trail entries
INSERT INTO data_submissions (user_id, table_name, record_id, action, old_data, new_data)
VALUES
  ('22222222-2222-2222-2222-222222222222', 'vehicles', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'create', NULL, '{"vin": "JT2BF22K1W0123456", "make": "Toyota", "model": "Corolla", "year": 2018}'::jsonb),
  ('33333333-3333-3333-3333-333333333333', 'accidents', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'create', NULL, '{"date": "2019-08-12", "severity": "minor"}'::jsonb);
