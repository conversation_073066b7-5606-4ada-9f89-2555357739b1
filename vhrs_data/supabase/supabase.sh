#!/bin/bash
# Helper script for common Supabase operations

# Make sure we're in the right directory
cd "$(dirname "$0")/.."

# Function to display help
show_help() {
  echo "VHRS Supabase Helper Script"
  echo ""
  echo "Usage: ./supabase.sh [command]"
  echo ""
  echo "Commands:"
  echo "  start       - Start Supabase services"
  echo "  stop        - Stop Supabase services"
  echo "  reset       - Reset the database (apply migrations)"
  echo "  seed        - Seed the database with test data"
  echo "  status      - Show status of Supabase services"
  echo "  studio      - Open Supabase Studio in browser"
  echo "  new-migration [name] - Create a new migration"
  echo "  push        - Push schema to Supabase cloud (requires login and link)"
  echo "  help        - Show this help message"
  echo ""
}

# Check if Docker is running
check_docker() {
  if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker Desktop first."
    exit 1
  fi
}

# Main command handler
case "$1" in
  start)
    check_docker
    echo "Starting Supabase services..."
    supabase start
    ;;
  stop)
    echo "Stopping Supabase services..."
    supabase stop
    ;;
  reset)
    check_docker
    echo "Resetting database..."
    supabase db reset
    ;;
  seed)
    check_docker
    echo "Seeding database..."
    PGPASSWORD=postgres psql -h 127.0.0.1 -p 54322 -U postgres -d postgres -f supabase/seed.sql
    ;;
  status)
    echo "Checking Supabase status..."
    supabase status
    ;;
  studio)
    echo "Opening Supabase Studio..."
    open http://127.0.0.1:54323
    ;;
  new-migration)
    if [ -z "$2" ]; then
      echo "Error: Migration name is required"
      echo "Usage: ./supabase.sh new-migration [name]"
      exit 1
    fi
    echo "Creating new migration: $2"
    supabase migration new "$2"
    ;;
  push)
    echo "Pushing schema to Supabase cloud..."
    supabase db push
    ;;
  help|*)
    show_help
    ;;
esac

exit 0
