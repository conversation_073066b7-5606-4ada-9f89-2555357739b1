-- <PERSON>ript to fix existing users that don't have entries in the profiles table or don't have roles assigned
-- This script should be run after applying the migrations

-- First, sync any auth.users that don't have corresponding entries in the profiles table
DO $$
DECLARE
  auth_user RECORD;
BEGIN
  RAISE NOTICE 'Starting to sync missing users...';

  FOR auth_user IN
    SELECT au.id, au.email, au.raw_user_meta_data, au.confirmed_at
    FROM auth.users au
    LEFT JOIN profiles u ON au.id = u.id
    WHERE u.id IS NULL
  LOOP
    RAISE NOTICE 'Syncing user: % (%)', auth_user.email, auth_user.id;

    -- Insert the user into the profiles table
    INSERT INTO profiles (
      id,
      email,
      full_name,
      created_at,
      is_verified
    )
    VALUES (
      auth_user.id,
      auth_user.email,
      COALESCE(auth_user.raw_user_meta_data->>'full_name', ''),
      NOW(),
      auth_user.confirmed_at IS NOT NULL
    );

    -- Assign the default 'user' role
    INSERT INTO user_roles (user_id, role_id)
    VALUES (
      auth_user.id,
      (SELECT id FROM roles WHERE name = 'user')
    )
    ON CONFLICT (user_id, role_id) DO NOTHING;
  END LOOP;

  RAISE NOTICE 'User sync completed.';
END;
$$;

-- Next, assign the default 'user' role to any users that don't have any roles
DO $$
DECLARE
  user_without_role RECORD;
  default_role_id UUID;
BEGIN
  -- Get the default user role ID
  SELECT roles.id INTO default_role_id FROM roles WHERE roles.name = 'user';

  RAISE NOTICE 'Starting to assign default roles to users without roles...';

  FOR user_without_role IN
    SELECT u.id, u.email
    FROM profiles u
    LEFT JOIN user_roles ur ON u.id = ur.user_id
    WHERE ur.user_id IS NULL
  LOOP
    RAISE NOTICE 'Assigning default role to user: % (%)', user_without_role.email, user_without_role.id;

    -- Assign the default 'user' role
    INSERT INTO user_roles (user_id, role_id)
    VALUES (user_without_role.id, default_role_id)
    ON CONFLICT (user_id, role_id) DO NOTHING;
  END LOOP;

  RAISE NOTICE 'Role assignment completed.';
END;
$$;

-- Finally, display a summary of users and their roles
SELECT
  u.id,
  u.email,
  u.full_name,
  array_agg(r.name) as roles
FROM
  profiles u
LEFT JOIN
  user_roles ur ON u.id = ur.user_id
LEFT JOIN
  roles r ON ur.role_id = r.id
GROUP BY
  u.id, u.email, u.full_name
ORDER BY
  u.email;
