# VHRS Development Guide

This guide provides instructions for setting up and running the Vehicle History Record System (VHRS) for development purposes.

## Prerequisites

Ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v16 or later)
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [Supabase CLI](https://supabase.com/docs/guides/cli)
- [Git](https://git-scm.com/)

## First-Time Setup

Follow these steps when setting up the project for the first time:

1. **Clone the repository (if you haven't already)**

   ```bash
   git clone <repository-url>
   cd vhrs
   ```

2. **Start Docker Desktop**

   Ensure Docker Desktop is running before proceeding.

3. **Initialize Supabase**

   ```bash
   cd vhrs_data
   supabase init
   ```

4. **Start Supabase services**

   ```bash
   supabase start
   ```

   This will start all Supabase services including PostgreSQL, PostgREST, GoTrue, and Storage.

5. **Apply database schema**

   ```bash
   supabase db reset
   ```

   This will apply all migrations and set up the database schema.

6. **Install dependencies for the Realtime Processor service**

   ```bash
   cd ../services
   npm install
   ```

7. **Set up environment variables**

   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file and fill in the required values. You can find the Supabase URL and keys in the output of the `supabase start` command.

8. **Start the Realtime Processor service**

   ```bash
   cd ../services
   ./start-realtime-processor.sh
   ```

9. **Access Supabase Studio**

   Open [http://127.0.0.1:54323](http://127.0.0.1:54323) in your browser to access Supabase Studio.

## Subsequent Runs

For subsequent development sessions, follow these simplified steps:

1. **Start Docker Desktop**

   Ensure Docker Desktop is running.

2. **Start Supabase services**

   ```bash
   cd vhrs_data
   supabase start
   ```

3. **Start the Realtime Processor service**

   ```bash
   cd ../services
   ./start-realtime-processor.sh
   ```

4. **Access Supabase Studio**

   Open [http://127.0.0.1:54323](http://127.0.0.1:54323) in your browser.

## Working with Edge Functions

### Serve Edge Functions Locally

```bash
# Navigate to the vhrs_data directory
cd vhrs_data

# Serve all functions
supabase functions serve

# Serve a specific function
supabase functions serve generate_vehicle_history
```

### Test Edge Functions

```bash
# Test the generate_vehicle_history function
curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/generate_vehicle_history' \
  --header 'Authorization: Bearer <your-anon-key>' \
  --header 'Content-Type: application/json' \
  --data '{"vehicle_id":"<vehicle-id>"}'
```

## Common Development Tasks

### User Management

#### Default System Admin User

The system includes a default admin user that is created during database initialization:

- **Email**: `<EMAIL>`
- **Password**: vhrsAdmin.
- **User ID**: ********-0000-0000-0000-************
- **Roles**: admin, user

This user is intended to be used for all functions, triggers, and automations that require a user/profile ID. You can also use this account to log in to the system with full administrative privileges.

The default admin user is created by the migration file `20250510000000_add_system_admin_user.sql`.

#### How User Creation Works

When a user signs up through Supabase Auth, a database trigger automatically:

1. Creates an entry in the `profiles` table with the user's information
2. Assigns the default 'user' role to the new user

The trigger is defined in the migration file `20250506130000_consolidated_updates.sql`.

#### Manually Syncing Users

If you need to manually sync users from auth.users to the profiles table (for example, if some users were created before the trigger was implemented), you can use one of these methods:

1. Using the SQL function (recommended):

   ```sql
   -- Run this in the SQL editor to sync all users
   SELECT sync_auth_users();
   ```

2. Using the JavaScript utility:

   ```bash
   # Navigate to the vhrs_data directory
   cd vhrs_data

   # Run the sync script
   node sync-users.js
   ```

You can also test the user creation and role assignment process with:

```bash
# Test user creation and role assignment
node test-user-creation.js
```

These methods find users in auth.users that don't have corresponding entries in the profiles table and creates them with the default 'user' role.

For more detailed troubleshooting of user profiles and role assignments, you can use the `fix_existing_users.sql` script:

```bash
# Navigate to the vhrs_data directory
cd vhrs_data

# Connect to the Supabase database and run the script
supabase db reset  # First ensure all migrations are applied
psql $(supabase db connection-string) -f supabase/fix_existing_users.sql
```

This script:

1. Identifies and creates profiles for users in auth.users that don't have corresponding entries in the profiles table
2. Identifies users in the profiles table that don't have any roles and assigns them the default 'user' role
3. Provides detailed logging of all actions taken
4. Displays a summary report of all users and their assigned roles

Use this script when:

- You suspect data inconsistencies between auth.users and the profiles table
- You need to troubleshoot role assignment issues
- You want a detailed report of all users and their roles

### Creating a New Migration

After making schema changes:

```bash
cd vhrs_data
supabase migration new <migration_name>
```

### Applying Migrations

```bash
supabase db reset
```

### Stopping Services

When you're done with development:

```bash
# Stop Supabase services
supabase stop

# Stop Docker containers if needed
docker-compose down
```

## Troubleshooting

### Supabase Services Not Starting

If Supabase services fail to start:

1. Check if Docker Desktop is running
2. Try stopping and restarting Supabase:

   ```bash
   supabase stop
   supabase start
   ```

### Database Reset Fails

If `supabase db reset` fails:

1. Check for syntax errors in your migration files
2. Ensure there are no conflicting migrations
3. Try stopping and restarting Supabase before resetting

### Edge Functions Not Working

If edge functions aren't working:

1. Check if the function is being served locally
2. Verify the request format and parameters
3. Check the function logs for errors:

   ```bash
   supabase functions logs
   ```

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase CLI Reference](https://supabase.com/docs/reference/cli)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
