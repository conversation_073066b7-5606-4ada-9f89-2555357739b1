import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import { dirname, resolve } from 'path';
import { setTimeout } from 'timers/promises';
import { fileURLToPath } from 'url';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = resolve(__dirname, '.env');
if (fs.existsSync(envPath)) {
  console.log(`Loading environment from ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.log('No .env file found, using default values');
  dotenv.config();
}

// Validate required environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
  console.error('ERROR: Missing required environment variables (SUPABASE_URL, SUPABASE_SERVICE_KEY)');
  console.error('Make sure your .env file is properly configured');
  process.exit(1);
}

// Initialize Supabase client with service role key for full access
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Service identification
const SERVICE_ID = process.env.SERVICE_ID || 'realtime-processor-1';
const MAX_RETRIES = parseInt(process.env.MAX_RETRIES || '3', 10);
const PROCESSING_INTERVAL_MS = parseInt(process.env.PROCESSING_INTERVAL_MS || '5000', 10);

console.log(`Starting VHRS Realtime Processor (${SERVICE_ID})`);
console.log(`Connected to Supabase at ${process.env.SUPABASE_URL}`);

// Function to process a history record request
async function processHistoryRequest(record) {
  console.log(`Processing history request: ${record.id}`);

  try {
    // Mark as processing
    await supabase
      .from('history_report_requests')
      .update({
        status: 'processing',
        processed_by: SERVICE_ID,
        retry_count: record.retry_count + 1
      })
      .eq('id', record.id);

    // Validate that vehicle_id is present
    if (!record.vehicle_id) {
      throw new Error('Missing required vehicle_id for history report request');
    }

    // Call the Edge Function (only sending vehicle_id as that's all it accepts)
    const { data, error } = await supabase.functions.invoke('generate_vehicle_history', {
      body: {
        vehicle_id: record.vehicle_id
      }
    });

    if (error) {
      throw new Error(`Edge function error: ${error.message}`);
    }

    // Update with successful result
    await supabase
      .from('history_report_requests')
      .update({
        history_report: data.history,
        status: 'generated',
        completed_at: new Date().toISOString(),
        last_error: null
      })
      .eq('id', record.id);

    console.log(`Successfully processed request: ${record.id}`);
    return true;
  } catch (error) {
    console.error(`Error processing request ${record.id}:`, error);

    // Determine if we should retry or mark as failed
    const newStatus = record.retry_count >= MAX_RETRIES ? 'failed' : 'pending';

    // Update with error information
    await supabase
      .from('history_report_requests')
      .update({
        status: newStatus,
        last_error: error.message
      })
      .eq('id', record.id);

    return false;
  }
}

// Function to find and process pending requests
async function processPendingRequests() {
  const { data: pendingRequests, error } = await supabase
    .from('history_report_requests')
    .select('*')
    .eq('status', 'pending')
    .order('created_at', { ascending: true })
    .limit(10);

  if (error) {
    console.error('Error fetching pending requests:', error);
    return;
  }

  if (pendingRequests.length === 0) {
    return;
  }

  console.log(`Found ${pendingRequests.length} pending requests`);

  // Process each request sequentially
  for (const request of pendingRequests) {
    await processHistoryRequest(request);
    // Small delay between processing to avoid overwhelming the system
    await setTimeout(500);
  }
}

// Set up Realtime subscription for new history report requests
const channel = supabase
  .channel('schema-db-changes')
  .on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'history_report_requests'
    },
    async (payload) => {
      console.log(`New history request detected: ${payload.new.id}`);
      // Process the new request
      await processHistoryRequest(payload.new);
    }
  )
  .subscribe((status) => {
    console.log(`Realtime subscription status: ${status}`);
  });

// Also periodically check for pending requests that might have been missed
// or failed in previous processing attempts
setInterval(processPendingRequests, PROCESSING_INTERVAL_MS);

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down...');
  await channel.unsubscribe();
  process.exit(0);
});

// Initial check for pending requests on startup
processPendingRequests();
