# VHRS Realtime Processor

This service listens for changes to the `history_report_requests` table in Supabase and processes them by calling the `generate_vehicle_history` Edge Function.

## Features

- Realtime subscription to new history report requests
- Periodic checking for pending requests that might have been missed
- Error handling with retry logic
- Tracking of processing status and errors

## Setup

1. Copy `.env.example` to `.env` and update the values if needed
2. Install dependencies:
   ```
   npm install
   ```
3. Start the service:
   ```
   npm start
   ```

## Development

For development with auto-restart on file changes:
```
npm run dev
```

## Environment Variables

- `SUPABASE_URL`: URL of your Supabase instance
- `SUPABASE_SERVICE_KEY`: Service role key for Supabase (has full access)
- `SERVICE_ID`: Unique identifier for this processor instance
- `MAX_RETRIES`: Maximum number of retry attempts for failed requests
- `PROCESSING_INTERVAL_MS`: Interval in milliseconds to check for pending requests

## Deployment

For production, you should run this service as a background process using a process manager like PM2 or deploy it to a hosting service.

### Using PM2

```
npm install -g pm2
pm2 start index.js --name vhrs-realtime-processor
pm2 save
```

### Docker

A Dockerfile is provided for containerized deployment.

```
docker build -t vhrs-realtime-processor .
docker run -d --name vhrs-processor vhrs-realtime-processor
```
