import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = resolve(__dirname, '.env');
if (fs.existsSync(envPath)) {
  console.log(`Loading environment from ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.log('No .env file found, using default values');
  dotenv.config();
}

// Validate required environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
  console.error('ERROR: Missing required environment variables (SUPABASE_URL, SUPABASE_SERVICE_KEY)');
  console.error('Make sure your .env file is properly configured');
  process.exit(1);
}

// Initialize Supabase client with service role key for full access
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

console.log(`Connected to Supabase at ${process.env.SUPABASE_URL}`);

// Function to insert a test history record request
async function insertTestRecord() {
  try {
    // Always create a new test vehicle for reliable testing
    console.log('Creating a new test vehicle...');

    // Create a test vehicle
    const { data: newVehicle, error: createError } = await supabase
      .from('vehicles')
      .insert({
        vin: 'TEST' + Math.floor(Math.random() * 1000000),
        make: 'Test Make',
        model: 'Test Model',
        year: 2023,
        vehicle_type: 'car'
      })
      .select();

    if (createError) {
      console.error('Error creating vehicle:', createError);
      return;
    }

    console.log('Created test vehicle:', newVehicle[0]);
    var vehicleId = newVehicle[0].id;

    // Insert a test history report request
    const { data, error } = await supabase
      .from('history_report_requests')
      .insert({
        vehicle_id: vehicleId,
        request_method: 'manual',
        status: 'pending',
        viewed_in_app: false
      })
      .select();

    if (error) {
      console.error('Error inserting history report request:', error);
      return;
    }

    console.log('Successfully inserted history report request:', data[0]);
    console.log('The Realtime processor should detect and process this request.');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the test
insertTestRecord();
