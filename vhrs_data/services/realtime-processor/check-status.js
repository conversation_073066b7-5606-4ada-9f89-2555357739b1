import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = resolve(__dirname, '.env');
if (fs.existsSync(envPath)) {
  console.log(`Loading environment from ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.log('No .env file found, using default values');
  dotenv.config();
}

// Validate required environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
  console.error('ERROR: Missing required environment variables (SUPABASE_URL, SUPABASE_SERVICE_KEY)');
  console.error('Make sure your .env file is properly configured');
  process.exit(1);
}

// Initialize Supabase client with service role key for full access
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

console.log(`Connected to Supabase at ${process.env.SUPABASE_URL}`);

// Function to check the status of history report requests
async function checkStatus() {
  try {
    // Get all history report requests
    const { data, error } = await supabase
      .from('history_report_requests')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      console.error('Error fetching history report requests:', error);
      return;
    }

    console.log('Recent history report requests:');
    data.forEach(record => {
      console.log(`ID: ${record.id}`);
      console.log(`Status: ${record.status}`);
      console.log(`Created: ${record.created_at}`);
      console.log(`Completed: ${record.completed_at || 'Not completed'}`);
      console.log(`Processed by: ${record.processed_by || 'Not processed'}`);
      console.log(`History report: ${JSON.stringify(record.history_report)}`);
      console.log('-----------------------------------');
    });

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the check
checkStatus();
