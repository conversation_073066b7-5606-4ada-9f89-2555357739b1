# VHRS Services

This directory contains supporting services for the Vehicle History Record System (VHRS).

## Services

### Realtime Processor

The Realtime Processor service listens for changes to the `history_report_requests` table in Supabase and processes them by calling the `generate_vehicle_history` Edge Function.

#### Features

- Realtime subscription to new history report requests
- Periodic checking for pending requests that might have been missed
- Error handling with retry logic
- Tracking of processing status and errors

#### Starting the Service

```bash
./start-realtime-processor.sh
```

For more details, see the [Realtime Processor README](./realtime-processor/README.md).

## Architecture

The VHRS system uses a decoupled architecture where:

1. The database stores data and maintains data integrity
2. Edge Functions provide business logic and processing capabilities
3. Realtime services connect the database to Edge Functions in an asynchronous manner

This approach provides several benefits:

- Better scalability - database operations complete quickly
- Improved resilience - temporary service outages don't affect data integrity
- Enhanced maintainability - components can be updated independently

## Development

When developing new services, follow these guidelines:

1. Create a dedicated directory for each service
2. Include a README.md with setup and usage instructions
3. Use environment variables for configuration
4. Implement proper error handling and logging
5. Include startup scripts in the root services directory
