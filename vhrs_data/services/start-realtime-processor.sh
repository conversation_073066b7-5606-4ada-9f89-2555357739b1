#!/bin/bash

# Script to start the VHRS Realtime Processor service

# Change to the realtime-processor directory
cd "$(dirname "$0")/realtime-processor"

# Check if .env file exists, if not create it from example
if [ ! -f .env ]; then
  echo "Creating .env file from .env.example..."
  cp .env.example .env
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

# Start the service
echo "Starting VHRS Realtime Processor..."
npm start
