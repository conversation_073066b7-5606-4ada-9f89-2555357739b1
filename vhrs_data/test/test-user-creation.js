// Test script to verify Supabase user creation and role assignment
import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Validate required environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
  console.error('ERROR: Missing required environment variables (SUPABASE_URL, SUPABASE_ANON_KEY)');
  console.error('Make sure your .env file is properly configured');
  process.exit(1);
}

// Initialize Supabase client with anon key for user operations
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Initialize Supabase admin client with service role key for checking database
const adminSupabase = process.env.SUPABASE_SERVICE_KEY
  ? createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY)
  : null;

console.log(`Connected to Supa<PERSON> at ${process.env.SUPABASE_URL}`);

// Generate a unique email for testing
const testEmail = `test-user-${randomUUID().substring(0, 8)}@example.com`;
const testPassword = 'Test123456!';
const testFullName = 'Test User';

// Function to create a test user
async function createTestUser() {
  try {
    console.log(`Creating test user with email: ${testEmail}`);

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: testFullName
        }
      }
    });

    if (authError) {
      console.error('Error creating user in auth:', authError);
      return null;
    }

    console.log('User created successfully in auth system');
    console.log('User ID:', authData.user.id);

    return authData.user;
  } catch (error) {
    console.error('Unexpected error creating user:', error);
    return null;
  }
}

// Function to check if user exists in users table
async function checkUserInDatabase(userId) {
  if (!adminSupabase) {
    console.warn('Admin Supabase client not initialized. Cannot check database tables.');
    console.warn('Please provide SUPABASE_SERVICE_KEY to check database tables.');
    return false;
  }

  try {
    // Wait a moment for the hook to process
    console.log('Waiting for hook to process...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check profiles table
    const { data: userData, error: userError } = await adminSupabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error checking users table:', userError);
      return false;
    }

    if (!userData) {
      console.log('User not found in profiles table');
      return false;
    }

    console.log('User found in profiles table:', userData);

    // Check user_roles table
    const { data: roleData, error: roleError } = await adminSupabase
      .from('user_roles')
      .select('*, roles:role_id(name)')
      .eq('user_id', userId);

    if (roleError) {
      console.error('Error checking user_roles table:', roleError);
      return false;
    }

    if (!roleData || roleData.length === 0) {
      console.log('User role not found in user_roles table');
      return false;
    }

    console.log('User role found in user_roles table:', roleData);
    return true;
  } catch (error) {
    console.error('Unexpected error checking database:', error);
    return false;
  }
}

// Function to manually sync users
async function syncUsers() {
  if (!adminSupabase) {
    console.warn('Admin Supabase client not initialized. Cannot sync users.');
    console.warn('Please provide SUPABASE_SERVICE_KEY to sync users.');
    return false;
  }

  try {
    console.log('Manually syncing user...');

    // Call the sync_auth_users function to sync the user
    const { data, error } = await adminSupabase
      .rpc('sync_auth_users');

    if (error) {
      console.error('Error syncing user:', error);
      return false;
    }

    console.log('User sync triggered successfully:', data);
    return true;
  } catch (error) {
    console.error('Unexpected error syncing users:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Check for command line arguments
    const args = process.argv.slice(2);
    const skipManualTrigger = args.includes('--skip-manual-trigger');

    // Create test user
    const user = await createTestUser();
    if (!user) {
      console.error('Failed to create test user');
      process.exit(1);
    }

    // Check if user exists in database
    console.log('Checking if user exists in database...');
    const userExists = await checkUserInDatabase(user.id);

    if (userExists) {
      console.log('SUCCESS: User was created in auth and profiles table');
      console.log('The database trigger is working correctly');
    } else {
      console.log('ISSUE DETECTED: User was created in auth but not in profiles table');
      console.log('The database trigger may not be working correctly');

      if (skipManualTrigger) {
        console.log('Skipping manual trigger as requested');
        console.log('User ID:', user.id);
        console.log('Email:', user.email);
      } else {
        // Try to manually sync users
        console.log('Attempting to manually sync users...');
        const triggered = await syncUsers();

        if (triggered) {
          // Check again after manual trigger
          console.log('Checking database again after manual trigger...');
          const userExistsAfterTrigger = await checkUserInDatabase(user.id);

          if (userExistsAfterTrigger) {
            console.log('SUCCESS: User was created in database after manually syncing users');
            console.log('The sync_auth_users function works, but the database trigger should be fixed');
          } else {
            console.log('ISSUE DETECTED: User was not created in database even after manually syncing users');
            console.log('There may be an issue with the sync_auth_users function');
          }
        }
      }
    }
  } catch (error) {
    console.error('Unexpected error in main function:', error);
  }
}

// Run the main function
main();
