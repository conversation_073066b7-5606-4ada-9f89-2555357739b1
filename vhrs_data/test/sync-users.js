// Script to manually sync users from auth.users to profiles table
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Validate required environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
  console.error('ERROR: Missing required environment variables (SUPABASE_URL, SUPABASE_SERVICE_KEY)');
  console.error('Make sure your .env file is properly configured');
  process.exit(1);
}

// Initialize Supabase client with service role key for admin privileges
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

console.log(`Connected to Supabase at ${process.env.SUPABASE_URL}`);

// Function to call the sync_auth_users database function
async function syncUsers() {
  try {
    console.log('Calling sync_auth_users() function...');
    
    const { data, error } = await supabase
      .rpc('sync_auth_users');
    
    if (error) {
      console.error('Error calling sync_auth_users function:', error);
      console.error('Make sure you have applied the migration that adds the sync_auth_users function');
      console.error('Run: supabase db reset');
      return false;
    }
    
    console.log('Sync completed successfully');
    console.log('Result:', data);
    return true;
  } catch (error) {
    console.error('Unexpected error syncing users:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    await syncUsers();
  } catch (error) {
    console.error('Unexpected error in main function:', error);
  }
}

// Run the main function
main();
