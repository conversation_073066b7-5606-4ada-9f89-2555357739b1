# VHRS Test Scripts

This directory contains test scripts for the Vehicle History Record System (VHRS), particularly focused on user management and authentication.

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create a `.env` file based on the `.env.example` template:
   ```bash
   cp .env.example .env
   ```

3. Update the `.env` file with your Supabase credentials if needed.

## Available Scripts

### Test User Creation

This script tests the user creation flow, including the database trigger that creates entries in the `profiles` table and assigns the default user role.

```bash
npm run test-user-creation
```

### Sync Users

This script manually syncs users from `auth.users` to the `profiles` table. It's useful if some users were created before the trigger was implemented or if the trigger failed for some reason.

```bash
npm run sync-users
```

## Troubleshooting

If you encounter issues with user creation:

1. Make sure Supabase is running locally:
   ```bash
   supabase status
   ```

2. Check if the database trigger is properly set up:
   ```sql
   -- Run this in the SQL editor
   SELECT pg_get_functiondef(oid) FROM pg_proc WHERE proname = 'sync_user_profile';
   ```

3. If the trigger is missing or incorrect, reset the database:
   ```bash
   supabase db reset
   ```

4. If users exist in `auth.users` but not in `profiles`, run the sync script:
   ```bash
   npm run sync-users
   ```
