# 🏗️ VHRS Backend Architecture

This document describes the technical architecture of the Vehicle History Record System (VHRS) backend, including implementation details, technology choices, and system interactions.

## 📦 Technology Stack

The VHRS backend is built on **Supabase**, a comprehensive Backend-as-a-Service (BaaS) platform that provides:

- **PostgreSQL Database**: For data storage with advanced features
- **Authentication**: For user identity management
- **Storage**: For file and media management
- **Edge Functions**: For serverless computing
- **Realtime**: For event-driven architecture
- **Row-Level Security (RLS)**: For data access control

## 🏛️ System Architecture

```mermaid
graph TD
    Client[Client Applications] --> Auth[Supabase Auth]
    Client --> API[Supabase API]
    Client --> Storage[Supabase Storage]
    Client --> EdgeFunctions[Edge Functions]

    API --> Database[(PostgreSQL Database)]
    API --> RLS[Row-Level Security]

    Database --> Triggers[Database Triggers]
    Database --> Realtime[Realtime]

    Realtime --> Processor[Realtime Processor Service]
    Processor --> EdgeFunctions

    EdgeFunctions --> Database
    EdgeFunctions --> Storage

    subgraph "External Services"
        Notifications[Notification Services]
        ThirdPartyAPIs[Third-Party APIs]
    end

    EdgeFunctions --> Notifications
    EdgeFunctions --> ThirdPartyAPIs
```

## 🗃️ Database Schema

The database uses a relational schema with the following key tables:

- **users**: User profiles and preferences
- **roles**: User role definitions (admin, user, and custom roles created by admins)
- **permissions**: Granular action permissions
- **vehicles**: Core vehicle information
- **ownership_history**: Vehicle ownership records
- **maintenance_records**: Service and maintenance history
- **accidents**: Accident records
- **mileage_records**: Odometer readings
- **alerts**: Fraud and issue alerts
- **history_report_requests**: Vehicle history report requests

### Schema Relationships

```mermaid
erDiagram
    USERS ||--o{ USER_ROLES : has
    ROLES ||--o{ USER_ROLES : assigned_to
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : assigned_to

    USERS ||--o{ VEHICLES : adds
    VEHICLES ||--o{ OWNERSHIP_HISTORY : has
    VEHICLES ||--o{ MAINTENANCE_RECORDS : has
    VEHICLES ||--o{ ACCIDENTS : has
    VEHICLES ||--o{ MILEAGE_RECORDS : has
    VEHICLES ||--o{ ALERTS : has

    USERS ||--o{ HISTORY_RECORD_REQUESTS : creates
    VEHICLES ||--o{ HISTORY_RECORD_REQUESTS : requested_for
```

## 🔐 Authentication & Authorization

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Client
    participant Auth as Supabase Auth
    participant DB as Database

    User->>Client: Enter credentials
    Client->>Auth: Submit credentials
    Auth->>Auth: Validate credentials
    Auth->>Client: Return JWT token
    Auth->>DB: Trigger sync_user_profile
    DB->>DB: Create/update user record
```

### Authorization with RLS

Row-Level Security (RLS) policies control data access based on user roles:

- **Users**: Can only access their own data
- **Data Agents**: Can access and modify vehicle records
- **Admins**: Have full system access

## 🔄 Event-Driven Architecture

The system uses an event-driven approach for asynchronous processing:

```mermaid
sequenceDiagram
    participant Client
    participant API as Supabase API
    participant DB as Database
    participant Realtime as Supabase Realtime
    participant Processor as Realtime Processor
    participant EdgeFunc as Edge Functions

    Client->>API: Create vehicle history record request
    API->>DB: Insert record (status: pending)
    DB->>Realtime: Emit database change
    Realtime->>Processor: Notify of new request
    Processor->>EdgeFunc: Call generate_vehicle_history
    EdgeFunc->>DB: Fetch vehicle data
    EdgeFunc->>DB: Update request (status: generated)
    DB->>Realtime: Emit status change
    Realtime->>Client: Notify completion
```

## 🚀 Edge Functions

Edge Functions provide serverless computing capabilities:

### generate_vehicle_history

This function generates comprehensive vehicle history records:

```mermaid
flowchart TD
    A[Receive Request] --> B{Validate vehicle_id}
    B -->|Invalid| C[Return Error]
    B -->|Valid| D[Query Database]
    D --> E[Fetch Vehicle Details]
    E --> F[Fetch Ownership History]
    F --> G[Fetch Maintenance Records]
    G --> H[Fetch Accident Records]
    H --> I[Compile History Object]
    I --> J[Return Response]
```

**Implementation Details:**

- Written in TypeScript using Deno runtime
- Accepts vehicle_id as input parameter
- Performs input validation with UUID format check
- Returns structured JSON response with vehicle history
- Includes error handling for various failure scenarios

## 🔔 Realtime Processing

The Realtime Processor service:

1. Listens for database changes via Supabase Realtime
2. Processes new history record requests
3. Calls Edge Functions to generate vehicle history records
4. Updates request status in the database

**Implementation Details:**

- Replaces previous HTTP trigger approach
- Provides better scalability and reliability
- Handles retries for failed processing attempts

## 🔒 Security Implementation

### Database-Level Security

- **RLS Policies**: Control data access based on user roles
- **Secure Functions**: Validate inputs and prevent SQL injection
- **Audit Logging**: Track all data modifications

### API Security

- **JWT Authentication**: Secure API access
- **HTTPS**: Encrypted data transmission
- **Input Validation**: Prevent malicious inputs

## 📊 Data Flow Architecture

```mermaid
flowchart LR
    subgraph "Client Layer"
        MobileApp[Mobile App]
        WebApp[Web App]
    end

    subgraph "API Layer"
        SupabaseAPI[Supabase API]
        EdgeFunctions[Edge Functions]
    end

    subgraph "Processing Layer"
        RealtimeProcessor[Realtime Processor]
        Triggers[Database Triggers]
    end

    subgraph "Data Layer"
        Database[(PostgreSQL)]
        Storage[File Storage]
    end

    MobileApp --> SupabaseAPI
    WebApp --> SupabaseAPI
    MobileApp --> EdgeFunctions
    WebApp --> EdgeFunctions

    SupabaseAPI --> Database
    EdgeFunctions --> Database
    EdgeFunctions --> Storage

    Database --> Triggers
    Database --> RealtimeProcessor
    RealtimeProcessor --> EdgeFunctions
```

## 🔄 Database Triggers and Functions

### sync_user_profile

Synchronizes user data between auth.users and the users table:

```sql
CREATE OR REPLACE FUNCTION sync_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  IF (TG_OP = 'INSERT') THEN
    INSERT INTO users (id, email, phone, full_name, created_at, is_verified)
    VALUES (NEW.id, NEW.email, NEW.phone, NEW.full_name, NOW(), NEW.is_verified);
  ELSIF (TG_OP = 'UPDATE') THEN
    UPDATE users
    SET email = NEW.email,
        phone = NEW.phone,
        full_name = NEW.full_name,
        is_verified = NEW.is_verified
    WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### detect_fraud

Automatically detects odometer rollback fraud:

```sql
CREATE OR REPLACE FUNCTION detect_fraud()
RETURNS TRIGGER AS $$
BEGIN
  IF (NEW.mileage < OLD.mileage) THEN
    INSERT INTO alerts (vehicle_id, alert_type_id, description, status, created_by, created_at)
    VALUES (NEW.vehicle_id, (SELECT id FROM alert_types WHERE name = 'odometer_rollback'),
            'Odometer rollback detected', 'pending', NEW.flagged_by, NOW());
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## 📡 External Integrations

The system integrates with external services for:

- **Push Notifications**: Using FCM or similar services
- **SMS Notifications**: Using Twilio or similar services
- **Email Delivery**: Using SMTP or email service providers
- **PDF Generation**: For vehicle history records

## 🔄 Deployment Architecture

```mermaid
flowchart TD
    subgraph "Development Environment"
        LocalDB[(Local Supabase)]
        LocalFunctions[Local Edge Functions]
    end

    subgraph "Production Environment"
        SupabaseCloud[Supabase Cloud]
        CloudFunctions[Cloud Edge Functions]
        CloudStorage[Cloud Storage]
    end

    GitRepo[Git Repository] --> |Deploy| SupabaseCloud
    GitRepo --> |Deploy| CloudFunctions

    Developer[Developer] --> GitRepo
    Developer --> LocalDB
    Developer --> LocalFunctions

    Users[End Users] --> SupabaseCloud
    Users --> CloudFunctions
    CloudFunctions --> CloudStorage
```

## 🔍 Monitoring and Logging

- **Function Logs**: Track Edge Function execution and errors
- **Database Logs**: Monitor database performance and queries
- **Audit Logs**: Track data changes for security and compliance
- **Error Tracking**: Capture and report system errors

## 🚀 Scaling Considerations

- **Database Scaling**: Handled by Supabase's managed PostgreSQL
- **Edge Function Scaling**: Serverless auto-scaling
- **Storage Scaling**: Automatic with Supabase Storage
- **Rate Limiting**: Implemented for API endpoints to prevent abuse

## 🔄 Future Architecture Enhancements

- **Caching Layer**: Redis or similar for performance optimization
- **Analytics Pipeline**: For business intelligence and reporting
- **Machine Learning Integration**: For advanced fraud detection
- **Microservices**: For specific high-load components
