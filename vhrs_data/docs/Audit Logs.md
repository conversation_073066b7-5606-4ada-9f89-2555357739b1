# Audit Logs System

## Overview

The VHRS Audit Logs system provides comprehensive tracking of all data changes within the application. It automatically logs create, update, and delete operations on important database tables, allowing administrators to monitor system activity, investigate issues, and maintain compliance with regulatory requirements.

## Features

- **Automatic Logging**: All changes to critical tables are automatically logged
- **User Attribution**: Each log entry includes the user who made the change
- **Detailed Change Tracking**: For updates, both old and new values are stored
- **Filtering Capabilities**: Filter logs by table, action type, or user
- **Search Functionality**: Search logs by record ID or table name
- **Pagination**: Navigate through large sets of logs with ease
- **Export to CSV**: Export filtered logs for offline analysis or reporting

## Database Structure

The audit logs are stored in the `data_submissions` table with the following structure:

```sql
CREATE TABLE data_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  table_name TEXT NOT NULL, -- e.g., 'vehicles'
  record_id UUID NOT NULL,
  action TEXT NOT NULL CHECK (action IN ('create', 'update', 'delete')),
  old_data JSONB, -- Previous state (for updates/deletes)
  new_data JSONB, -- New state (for creates/updates)
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Audit Query Optimization
CREATE INDEX idx_data_submissions_user ON data_submissions(user_id, timestamp DESC);
```

## Automatic Logging Mechanism

Changes are automatically logged using PostgreSQL triggers. The `log_data_change()` function is triggered after insert, update, or delete operations on monitored tables:

```sql
CREATE OR REPLACE FUNCTION log_data_change()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO data_submissions (user_id, table_name, record_id, action, new_data, timestamp)
    VALUES (auth.uid(), TG_TABLE_NAME, NEW.id, 'create', row_to_json(NEW), NOW());
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO data_submissions (user_id, table_name, record_id, action, old_data, new_data, timestamp)
    VALUES (auth.uid(), TG_TABLE_NAME, NEW.id, 'update', row_to_json(OLD), row_to_json(NEW), NOW());
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO data_submissions (user_id, table_name, record_id, action, old_data, timestamp)
    VALUES (auth.uid(), TG_TABLE_NAME, OLD.id, 'delete', row_to_json(OLD), NOW());
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Monitored Tables

The following tables are monitored for changes:

- `vehicles`
- `vehicle_license_plates`
- `vehicle_colors`
- `ownership_history`
- `accidents`
- `maintenance_records`
- `mileage_records`

### Tables Not Monitored

The following tables are intentionally excluded from audit logging to reduce noise and focus on vehicle-related changes:

- `alerts`
- `alert_media`
- `history_report_requests`
- `users`
- `user_roles`

## Security

Access to audit logs is restricted to administrators only through Row-Level Security (RLS) policies:

```sql
CREATE POLICY audit_logs_admin_policy ON data_submissions
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid()
              AND r.name = 'admin')
  );
```

## Web Interface

The audit logs can be accessed through the web interface at `/dashboard/audit-logs`. This page provides:

1. **Filtering Options**:
   - Filter by table name
   - Filter by action type (create, update, delete)
   - Filter by user
   - Search by record ID or table name

2. **Log Details**:
   - Timestamp of the action
   - User who performed the action
   - Action type (color-coded)
   - Table name
   - Record ID
   - Detailed view showing old and new data

3. **Pagination**:
   - Navigate through large sets of logs
   - Jump to first/last page
   - Page size of 10 logs per page

4. **Export**:
   - Export filtered logs to CSV format

## Best Practices

1. **Regular Review**: Administrators should regularly review audit logs to identify suspicious activities
2. **Retention Policy**: Implement a retention policy for audit logs based on regulatory requirements
3. **Performance Monitoring**: Monitor the size of the `data_submissions` table and archive old logs if necessary
4. **Sensitive Data**: Be aware that audit logs may contain sensitive data and should be protected accordingly

## Troubleshooting

If audit logs are not being generated:

1. Check that the triggers are properly installed on the tables
2. Verify that the `log_data_change()` function exists and is working correctly
3. Ensure that the user performing the action has a valid `auth.uid()`
4. Check for any errors in the PostgreSQL logs

## Future Enhancements

Planned enhancements for the audit logs system include:

1. **Date Range Filtering**: Filter logs by date range
2. **Advanced Diff View**: Better visualization of changes between old and new data
3. **Automated Alerts**: Set up alerts for specific types of changes
4. **Archiving**: Automated archiving of old logs to maintain performance
