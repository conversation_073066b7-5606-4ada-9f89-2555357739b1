# **Database Schema**

##  **Version 1.1**

---

### **1. System Overview**

A PostgreSQL schema optimized for Uganda's vehicle history tracking, featuring:

- **Supabase Authentication Integration**
- Granular **Role-Based Access Control (RBAC)**
- Full **Vehicle Lifecycle Tracking** (Ownership, Accidents, Maintenance)
- **Fraud Detection**
- **Push Notification Support**
- **Audit Trails** for data changes
- **Asynchronous Processing** for history record generation

---

### **2. Core Tables**

### **A. User Management**

**1. `profiles`**

```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT, -- Nullable, but unique when provided
  phone TEXT, -- Nullable, but unique when provided
  full_name TEXT,
  preferences JSONB DEFAULT '{}'::jsonb, -- e.g., {"language":"en", "sms_alerts":true}
  notification_token TEXT, -- For push notifications (FCM/APNS)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  is_verified BOOLEAN DEFAULT false,
  last_active_at TIMESTAMPTZ -- Last user activity
);

-- Optimized Indexes
CREATE UNIQUE INDEX idx_profiles_email_partial ON profiles(email) WHERE email IS NOT NULL;
CREATE UNIQUE INDEX idx_profiles_phone_partial ON profiles(phone) WHERE phone IS NOT NULL;
CREATE INDEX idx_profiles_notification_token ON profiles(notification_token)
  WHERE notification_token IS NOT NULL;
```

**2. `roles`**

```sql
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL, -- e.g., 'admin', 'data_agent'
  description TEXT
);

-- Default Roles
INSERT INTO roles (name, description) VALUES
  ('admin', 'Full system access'),
  ('user', 'Regular user access');

-- Admins can create additional custom roles through the dashboard
```

**3. `permissions`**

```sql
CREATE TABLE permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code TEXT UNIQUE NOT NULL, -- e.g., 'vehicle:create'
  description TEXT NOT NULL
);

-- Core Permissions
INSERT INTO permissions (code, description) VALUES
  ('vehicle:create', 'Add new vehicles'),
  ('vehicle:edit', 'Modify vehicle data'),
  ('fraud:verify', 'Confirm fraud alerts'),
  ('ownership:transfer', 'Update ownership records');
```

**4. `role_permissions`**

```sql
CREATE TABLE role_permissions (
  role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
  PRIMARY KEY (role_id, permission_id)
);
```

**5. `user_roles`**

```sql
CREATE TABLE user_roles (
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
  PRIMARY KEY (user_id, role_id)
);
```

**6. System Admin User**

```sql
-- Default System Admin User (if not exists)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>') THEN
    INSERT INTO profiles (id, email, full_name, is_verified)
    VALUES (
      gen_random_uuid(),
      '<EMAIL>',
      'System Admin',
      true
    );

    -- Assign admin role to System Admin
    INSERT INTO user_roles (user_id, role_id)
    VALUES (
      (SELECT id FROM profiles WHERE email = '<EMAIL>'),
      (SELECT id FROM roles WHERE name = 'admin')
    );
  END IF;
END
$$;
```

---

### **B. Vehicle Data**

**1. `vehicles`**

```sql
CREATE TABLE vehicles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vin TEXT UNIQUE NOT NULL, -- Vehicle Identification Number
  make TEXT NOT NULL, -- e.g., Toyota
  model TEXT NOT NULL, -- e.g., Hilux
  year INTEGER CHECK (year > 1900),
  vehicle_type TEXT CHECK (vehicle_type IN ('car', 'motorcycle', 'truck')),
  added_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance Indexes
CREATE INDEX idx_vehicles_vin ON vehicles(vin);
CREATE INDEX idx_vehicles_make_model ON vehicles(make, model);
```

**2.** `vehicle_license_plates`

```sql
CREATE TABLE vehicle_license_plates (  -- Note: Corrected "plates" spelling
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID NOT NULL REFERENCES vehicles(id),
  license_plate TEXT NOT NULL,
  registration_centre TEXT,  -- e.g., "URA Kampala", "Mbale DTS Office"
  start_date DATE NOT NULL, -- if start is unknown, today is used
  end_date DATE,  -- NULL = currently active
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add constraint to prevent overlapping date ranges for the same vehicle
ALTER TABLE vehicle_license_plates ADD CONSTRAINT no_overlapping_plates
  EXCLUDE USING gist (vehicle_id WITH =, daterange(start_date, end_date, '[]') WITH &&);

-- Indexes
CREATE INDEX idx_plates_vehicle ON vehicle_license_plates(vehicle_id);
CREATE INDEX idx_plates_active ON vehicle_license_plates(license_plate) WHERE end_date IS NULL;
```

**3.** `vehicle_colors`

```sql
CREATE TABLE vehicle_colors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID NOT NULL REFERENCES vehicles(id),
  color TEXT NOT NULL,
  effective_date DATE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_colors_vehicle ON vehicle_colors(vehicle_id);
```

**4. `ownership_history`**

```sql
CREATE TABLE ownership_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  owner_name TEXT NOT NULL,
  owner_phone TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  is_current BOOLEAN DEFAULT true,
  proof_url TEXT -- PDF/image URL of ownership document
);

-- Index for Active Ownership Lookups
CREATE INDEX idx_current_owners ON ownership_history(vehicle_id)
  WHERE is_current = true;
```

**5. `accidents`**

```sql
CREATE TABLE accidents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  severity TEXT CHECK (severity IN ('minor', 'major', 'totaled')),
  photos TEXT[], -- Array of image URLs
  reported_by UUID REFERENCES users(id)
);

-- Query Optimization
CREATE INDEX idx_accidents_vehicle_date ON accidents(vehicle_id, date DESC);
```

**6. `maintenance_records`**

```sql
CREATE TABLE maintenance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  service_type TEXT NOT NULL, -- e.g., 'oil_change'
  date DATE NOT NULL,
  mileage INTEGER,
  garage_name TEXT,
  receipt_url TEXT -- Link to service receipt
);

-- Service History Index
CREATE INDEX idx_maintenance_vehicle ON maintenance_records(vehicle_id, date DESC);
```

**7. `mileage_records`**

```sql
CREATE TABLE mileage_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
  mileage INTEGER NOT NULL,
  date DATE NOT NULL,
  source TEXT NOT NULL CHECK (source IN ('user', 'garage', 'inspection')),
  is_suspicious BOOLEAN DEFAULT false,
  flagged_by UUID REFERENCES users(id)
);

-- Fraud Analysis Index
CREATE INDEX idx_mileage_suspicious ON mileage_records(vehicle_id)
  WHERE is_suspicious = true;
```

**8. `alert_types`**

```sql
CREATE TABLE alert_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL, -- e.g., 'odometer_rollback'
  description TEXT,          -- Optional description
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Default Alert Types
INSERT INTO alert_types (name, description) VALUES
  ('odometer_rollback', 'Suspected tampering with odometer reading'),
  ('cloned_vin', 'Vehicle with duplicated VIN detected'),
  ('accident_report', 'Unreported accident information'),
  ('stolen_vehicle', 'Vehicle reported as stolen'),
  ('other', 'Other type of alert');
```

**9. `alerts`**

```sql
CREATE TABLE alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE, -- nullable
  license_plate TEXT, -- new field
  alert_type_id UUID REFERENCES alert_types(id) ON DELETE RESTRICT NOT NULL, -- link to alert_types
  confidence_score FLOAT CHECK (confidence_score >= 0 AND confidence_score <= 1),
  location GEOMETRY(POINT, 4326), -- Latitude/Longitude (WGS84)
  description TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected')),
  resolved_action TEXT, -- e.g., 'moved_to_accidents'
  resolved_by UUID REFERENCES users(id),
  created_by UUID REFERENCES users(id), -- User who created the alert
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_alerts_location ON alerts USING GIST(location);
CREATE INDEX idx_alerts_type_status ON alerts(alert_type_id, status);
CREATE INDEX idx_alerts_license_plate ON alerts(license_plate);
CREATE INDEX idx_alerts_created_by ON alerts(created_by);
```

**10. `alert_media`**

```sql
CREATE TABLE alert_media (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_id UUID REFERENCES alerts(id) ON DELETE CASCADE NOT NULL,
  media_url TEXT NOT NULL,
  media_type TEXT CHECK (media_type IN ('image', 'video', 'document')),
  description TEXT, -- Optional caption/context
  details JSONB DEFAULT '{}'::jsonb, -- e.g., {"source":"iPhone", "f":0.5}
  uploaded_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index for quick media retrieval per alert
CREATE INDEX idx_alert_media_parent ON alert_media(alert_id);
```

**11. `history_report_requests`**

```sql
CREATE TABLE history_report_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  vehicle_id UUID REFERENCES vehicles(id), -- Optional, nullable if report is based on license_plate only
  license_plate TEXT, -- Optional, when VIN is not available
  vin TEXT, -- Optional, when plate is not available
  request_method TEXT NOT NULL CHECK (request_method IN ('vin', 'license_plate', 'manual')), -- How user searched
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'generated', 'failed')),
  document_url TEXT, -- URL to downloadable PDF, if generated
  history_report JSONB DEFAULT '{}'::jsonb, -- Stores the generated report as a JSON object
  viewed_in_app BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ, -- When report was generated (for audit/perf)
  processed_by TEXT, -- Identifier of the service that processed this request
  retry_count INTEGER DEFAULT 0, -- Counter for processing attempts (helps with troubleshooting)
  last_error TEXT -- Stores error messages if processing failed
);

-- Indexes
CREATE INDEX idx_report_requests_user ON history_report_requests(user_id, created_at DESC);
CREATE INDEX idx_report_requests_vehicle ON history_report_requests(vehicle_id);
CREATE INDEX idx_history_report_requests_status ON history_report_requests(status) WHERE status = 'pending';
```

---

### **C. System Operations**

**1. `data_submissions` (Audit Trail)**

```sql
CREATE TABLE data_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  table_name TEXT NOT NULL, -- e.g., 'vehicles'
  record_id UUID NOT NULL,
  action TEXT NOT NULL CHECK (action IN ('create', 'update', 'delete')),
  old_data JSONB, -- Previous state (for updates/deletes)
  new_data JSONB, -- New state (for creates/updates)
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Audit Query Optimization
CREATE INDEX idx_data_submissions_user ON data_submissions(user_id, timestamp DESC);
```

---

### **3. Database Functions and Triggers**

**1. User Profile Sync**

```sql
-- Trigger to sync user profiles between auth.users and users table
CREATE OR REPLACE FUNCTION sync_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  IF (TG_OP = 'INSERT') THEN
    INSERT INTO users (id, email, phone, full_name, created_at, is_verified)
    VALUES (NEW.id, NEW.email, NEW.phone, NEW.full_name, NOW(), NEW.is_verified);
  ELSIF (TG_OP = 'UPDATE') THEN
    UPDATE users
    SET email = NEW.email,
        phone = NEW.phone,
        full_name = NEW.full_name,
        is_verified = NEW.is_verified
    WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for user profile sync
CREATE TRIGGER trigger_sync_user_profile
AFTER INSERT OR UPDATE ON auth.users
FOR EACH ROW
EXECUTE FUNCTION sync_user_profile();
```

**2. Automated Fraud Detection**

```sql
-- Function for automated fraud detection
CREATE OR REPLACE FUNCTION detect_fraud()
RETURNS TRIGGER AS $$
BEGIN
  IF (NEW.mileage < OLD.mileage) THEN
    INSERT INTO alerts (vehicle_id, alert_type_id, description, status, created_by, created_at)
    VALUES (NEW.vehicle_id, (SELECT id FROM alert_types WHERE name = 'odometer_rollback'),
            'Odometer rollback detected', 'pending', NEW.flagged_by, NOW());
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for fraud detection
CREATE TRIGGER trigger_detect_fraud
AFTER UPDATE ON mileage_records
FOR EACH ROW
WHEN (OLD.mileage IS NOT NULL AND NEW.mileage < OLD.mileage)
EXECUTE FUNCTION detect_fraud();
```

---

### **4. Security Policies**

**Row-Level Security (RLS) Examples:**

```sql
-- Admins Only: Delete Vehicles
CREATE POLICY vehicle_delete_policy ON vehicles
  FOR DELETE USING (
    EXISTS (SELECT 1 FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid()
              AND r.name = 'admin')
  );

-- Agents with vehicle:edit permission
CREATE POLICY vehicle_edit_policy ON vehicles
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      JOIN roles r ON ur.role_id = r.id
      JOIN role_permissions rp ON rp.role_id = r.id
      JOIN permissions p ON p.id = rp.permission_id
      WHERE ur.user_id = auth.uid()
        AND p.code = 'vehicle:edit'
    )
  );
```

---

### **5. Extensions**

The database uses the following PostgreSQL extensions:

```sql
-- Enable PostGIS for location data
CREATE EXTENSION IF NOT EXISTS postgis;

-- Enable btree_gist for range constraints
CREATE EXTENSION IF NOT EXISTS btree_gist;
```

---

### **6. Asynchronous Processing**

The system processes history report requests asynchronously:

1. When a new record is inserted into `history_report_requests`, it starts with status 'pending'
2. A background service detects the new record and calls the Edge Function to generate the history
3. The service updates the record with the generated history and changes status to 'generated'
4. If processing fails, the service tracks the error and can retry the operation

This asynchronous approach provides several benefits:

- Better scalability for handling large volumes of requests
- Improved reliability with automatic retry mechanisms
- Detailed tracking of processing status and errors

---

### **7. Relationships Summary**

- `vehicles` ⬌ `ownership_history`, `vehicle_license_plates`, `vehicle_colors`, `alerts`
- `alerts` ⬌ `alert_media`
- `users` ⬌ `user_roles` ⬌ `roles` ⬌ `role_permissions` ⬌ `permissions`
- `users` ⬌ `data_submissions`
- `vehicles` ⬌ `accidents`, `mileage_records`, `maintenance_records`

### **8. Key Features**

- **Granular Permissions**: 25+ configurable actions via **`role_permissions`**
- **Ownership Chain**: Track current/past owners with document proof
- **Fraud Detection**: Flag odometer rollbacks and cloned VINs
- **Mobile-First**: Push notifications via **`notification_token`**
- **Audit-Ready**: Full history of data changes in **`data_submissions`**
- **Asynchronous Processing**: Background processing for history record generation
- **Error Handling**: Robust tracking and retry mechanisms for reliability
- **System Admin**: Dedicated system account for automated operations
